// 事件管理模块 - 处理全局事件、消息传递、状态同步等功能
import { BaseModule } from './BaseModule.js';

export class EventManager extends BaseModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // 事件相关状态
        this.messageListeners = new Map();
        this.stateUpdateCallbacks = new Set();
        this.lastStateUpdateTime = 0;
        this.stateCheckInterval = null;
        this.pendingAutoPlay = false;
        
        // 消息区域
        this.messageAreaEl = null;
        this.footerEl = null;
        
        // 连接检查
        this.connectionCheckInterval = null;
        this.lastConnectionCheck = 0;
    }

    async onInit() {
        // 获取DOM元素
        this.initDOMElements();
        
        // 设置全局事件监听器
        this.setupGlobalEventListeners();
        
        // 设置消息监听器
        this.setupMessageListeners();
        
        // 启动状态同步
        this.startStateSynchronization();
        
        // 启动连接检查
        this.startConnectionCheck();
        
        // 设置窗口关闭处理
        this.setupWindowCloseHandling();
    }

    initDOMElements() {
        this.messageAreaEl = document.getElementById('message-area');
        this.footerEl = document.querySelector('.panel-footer');
    }

    setupGlobalEventListeners() {
        // 页面可见性变化
        this.addEventListener(document, 'visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                console.log("侧边栏可见性恢复，重新请求状态");
                this.requestStateUpdate();
            }
        });

        // 窗口焦点事件
        this.addEventListener(window, 'focus', () => {
            console.log("侧边栏获得焦点，检查状态");
            // 如果上次状态更新超过3秒，请求新状态
            if (Date.now() - this.lastStateUpdateTime > 3000) {
                this.requestStateUpdate();
            }
        });

        // 窗口失焦事件
        this.addEventListener(window, 'blur', () => {
            console.log("侧边栏失去焦点");
        });

        // 键盘快捷键
        this.addEventListener(document, 'keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // 右键菜单
        this.addEventListener(document, 'contextmenu', (e) => {
            // 在开发环境允许右键菜单，生产环境可以禁用
            if (!this.dependencies.configManager.getConfig('debug.allowContextMenu', true)) {
                e.preventDefault();
            }
        });
    }

    setupMessageListeners() {
        // Chrome runtime消息监听
        if (this.dependencies.chrome.runtime.onMessage) {
            this.dependencies.chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                this.handleRuntimeMessage(message, sender, sendResponse);
            });
        }

        // 存储变化监听
        if (this.dependencies.chrome.storage.onChanged) {
            this.dependencies.chrome.storage.onChanged.addListener((changes, namespace) => {
                this.handleStorageChange(changes, namespace);
            });
        }
    }

    // 处理运行时消息
    handleRuntimeMessage(message, sender, sendResponse) {
        console.log('收到运行时消息:', message);
        
        switch (message.action) {
            case 'stateUpdate':
                this.handleStateUpdate(message.state);
                break;
                
            case 'playbackProgress':
                this.handlePlaybackProgress(message.progress);
                break;
                
            case 'error':
                this.handleError(message.error);
                break;
                
            case 'notification':
                this.handleNotification(message.notification);
                break;
                
            case 'voiceSettingsUpdate':
                this.handleVoiceSettingsUpdate(message);
                break;
                
            default:
                console.log('未处理的消息类型:', message.action);
        }
        
        // 发送响应
        if (sendResponse) {
            sendResponse({ received: true });
        }
    }

    // 处理存储变化
    handleStorageChange(changes, namespace) {
        console.log('存储变化:', changes, namespace);
        
        // 通知相关模块存储变化
        for (const [key, change] of Object.entries(changes)) {
            this.notifyStorageChange(key, change.oldValue, change.newValue);
        }
    }

    // 处理状态更新
    handleStateUpdate(state) {
        this.lastStateUpdateTime = Date.now();
        
        // 通知所有状态更新回调
        this.stateUpdateCallbacks.forEach(callback => {
            try {
                callback(state);
            } catch (error) {
                this.errorHandler.handleError(error, 'state_update_callback');
            }
        });
        
        // 通知其他模块状态更新
        this.notifyModulesStateUpdate(state);
    }

    // 处理播放进度
    handlePlaybackProgress(progress) {
        // 通知播放控制器更新进度
        const playbackController = this.dependencies.moduleContainer.getModule('PlaybackController');
        if (playbackController && playbackController.updateProgress) {
            playbackController.updateProgress(progress);
        }
    }

    // 处理错误
    handleError(error) {
        console.error('收到错误消息:', error);
        this.showMessage(error.message || '发生未知错误', true);
        
        // 通知错误处理器
        this.errorHandler.handleError(new Error(error.message), error.context || 'runtime_message');
    }

    // 处理通知
    handleNotification(notification) {
        console.log('收到通知:', notification);
        this.showMessage(notification.message, notification.type === 'error');
    }

    // 处理语音设置更新
    handleVoiceSettingsUpdate(message) {
        // 通知语音管理器更新设置
        const voiceManager = this.dependencies.moduleContainer.getModule('VoiceManager');
        if (voiceManager) {
            if (message.defaultVoice) {
                voiceManager.setDefaultVoice(message.defaultVoice);
            }
            if (message.favoriteVoices) {
                voiceManager.updateFavoriteVoices(message.favoriteVoices);
            }
        }
    }

    // 处理键盘快捷键
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + Space: 播放/暂停
        if ((e.ctrlKey || e.metaKey) && e.code === 'Space') {
            e.preventDefault();
            const playbackController = this.dependencies.moduleContainer.getModule('PlaybackController');
            if (playbackController) {
                playbackController.handlePlayPauseClick();
            }
        }
        
        // Ctrl/Cmd + S: 停止
        if ((e.ctrlKey || e.metaKey) && e.code === 'KeyS') {
            e.preventDefault();
            const playbackController = this.dependencies.moduleContainer.getModule('PlaybackController');
            if (playbackController) {
                playbackController.handleStopClick();
            }
        }
        
        // Ctrl/Cmd + ,: 打开设置
        if ((e.ctrlKey || e.metaKey) && e.code === 'Comma') {
            e.preventDefault();
            const settingsManager = this.dependencies.moduleContainer.getModule('SettingsManager');
            if (settingsManager) {
                settingsManager.openSettings();
            }
        }
        
        // Escape: 关闭设置或其他覆盖层
        if (e.code === 'Escape') {
            const settingsManager = this.dependencies.moduleContainer.getModule('SettingsManager');
            if (settingsManager && settingsManager.isSettingsOverlayOpen()) {
                settingsManager.closeSettings();
            }
        }
    }

    // 启动状态同步
    startStateSynchronization() {
        // 立即请求一次状态
        this.requestStateUpdate();
        
        // 定期检查状态同步
        this.stateCheckInterval = setInterval(() => {
            this.ensureStateSynchronization();
        }, 5000);
        
        console.log('状态同步已启动');
    }

    // 确保状态同步
    ensureStateSynchronization() {
        const now = Date.now();
        
        // 如果超过10秒没有状态更新，主动请求
        if (now - this.lastStateUpdateTime > 10000) {
            console.log('状态同步超时，主动请求更新');
            this.requestStateUpdate();
        }
    }

    // 启动连接检查
    startConnectionCheck() {
        this.connectionCheckInterval = setInterval(() => {
            this.checkBackgroundConnection();
        }, 30000); // 每30秒检查一次
        
        console.log('连接检查已启动');
    }

    // 检查后台连接
    checkBackgroundConnection() {
        const now = Date.now();
        this.lastConnectionCheck = now;
        
        // 发送ping消息检查连接
        this.dependencies.chrome.runtime.sendMessage({ action: 'ping' }, (response) => {
            if (this.dependencies.chrome.runtime.lastError) {
                console.warn('后台连接检查失败:', this.dependencies.chrome.runtime.lastError);
                this.handleConnectionLost();
            } else if (response && response.status === 'pong') {
                console.log('后台连接正常');
            } else {
                console.warn('后台连接响应异常:', response);
            }
        });
    }

    // 处理连接丢失
    handleConnectionLost() {
        console.error('与后台脚本的连接丢失');
        this.showMessage('与后台服务的连接丢失，请刷新页面', true);
        
        // 尝试重新连接
        setTimeout(() => {
            this.requestStateUpdate();
        }, 5000);
    }

    // 设置窗口关闭处理
    setupWindowCloseHandling() {
        this.addEventListener(window, 'beforeunload', (event) => {
            console.log("侧边栏即将关闭，检查是否为插件关闭");
            
            // 重置pendingAutoPlay标记
            this.pendingAutoPlay = false;
            
            // 尝试清除session存储中的标记
            try {
                this.dependencies.chrome.storage.session.remove('autoRefreshForPlay');
            } catch (e) {
                console.error("清除autoRefreshForPlay标记失败:", e);
            }
            
            // 检查是否为真正的插件关闭
            const isPluginClosing = this.checkIfPluginClosing();
            
            if (isPluginClosing) {
                console.log("插件真正关闭，发送停止命令");
                
                // 发送停止命令
                try {
                    this.dependencies.chrome.runtime.sendMessage({
                        action: 'sidePanelClosing',
                        force: true,
                        timestamp: Date.now()
                    });
                } catch (e) {
                    console.error("发送停止消息失败:", e);
                }
            }
        });
    }

    // 检查是否为插件关闭
    checkIfPluginClosing() {
        // 这里可以添加更复杂的逻辑来判断是否为真正的插件关闭
        // 目前简单返回true
        return true;
    }

    // 请求状态更新
    requestStateUpdate() {
        this.dependencies.chrome.runtime.sendMessage({ action: 'getState' }, (response) => {
            if (this.dependencies.chrome.runtime.lastError) {
                console.error('请求状态更新失败:', this.dependencies.chrome.runtime.lastError);
                this.handleConnectionLost();
            } else if (response) {
                this.handleStateUpdate(response);
            }
        });
    }

    // 发送消息到后台
    sendMessageToBackground(message) {
        return new Promise((resolve, reject) => {
            this.dependencies.chrome.runtime.sendMessage(message, (response) => {
                if (this.dependencies.chrome.runtime.lastError) {
                    reject(this.dependencies.chrome.runtime.lastError);
                } else {
                    resolve(response);
                }
            });
        });
    }

    // 显示消息
    showMessage(message, isError = false) {
        if (!this.messageAreaEl) {
            console.log(isError ? '❌' : 'ℹ️', message);
            return;
        }
        
        // 清除之前的消息
        this.hideMessage();
        
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message ${isError ? 'error' : 'info'}`;
        messageEl.textContent = message;
        
        this.messageAreaEl.appendChild(messageEl);
        this.messageAreaEl.style.display = 'block';
        
        // 自动隐藏消息
        setTimeout(() => {
            this.hideMessage();
        }, isError ? 5000 : 3000);
    }

    // 隐藏消息
    hideMessage() {
        if (this.messageAreaEl) {
            this.messageAreaEl.innerHTML = '';
            this.messageAreaEl.style.display = 'none';
        }
    }

    // 通知模块状态更新
    notifyModulesStateUpdate(state) {
        // 通知播放控制器
        const playbackController = this.dependencies.moduleContainer.getModule('PlaybackController');
        if (playbackController && playbackController.updateState) {
            playbackController.updateState(state);
        }
        
        // 通知URL处理器
        const urlHandler = this.dependencies.moduleContainer.getModule('URLHandler');
        if (urlHandler && state.currentPlayingURL) {
            urlHandler.updatePlayingContentInfo(state.currentTitle, state.currentPlayingURL);
        }
        
        // 通知设置管理器
        const settingsManager = this.dependencies.moduleContainer.getModule('SettingsManager');
        if (settingsManager && state.currentChapterTitle) {
            settingsManager.updateCurrentChapterTitle(state.currentChapterTitle);
        }
    }

    // 通知存储变化
    notifyStorageChange(key, oldValue, newValue) {
        // 根据不同的存储键通知相应的模块
        switch (key) {
            case 'favoriteVoices':
                const voiceManager = this.dependencies.moduleContainer.getModule('VoiceManager');
                if (voiceManager) {
                    voiceManager.loadFavoriteVoices();
                }
                break;
                
            case 'playbackHistory':
                const urlHandler = this.dependencies.moduleContainer.getModule('URLHandler');
                if (urlHandler) {
                    urlHandler.loadPlaybackHistory();
                }
                break;
                
            case 'defaultVoice':
                const voiceManager2 = this.dependencies.moduleContainer.getModule('VoiceManager');
                if (voiceManager2) {
                    voiceManager2.loadDefaultVoice();
                }
                break;
        }
    }

    // 注册状态更新回调
    onStateUpdate(callback) {
        this.stateUpdateCallbacks.add(callback);
        
        // 返回取消注册的函数
        return () => {
            this.stateUpdateCallbacks.delete(callback);
        };
    }

    // 注册消息监听器
    onMessage(type, callback) {
        if (!this.messageListeners.has(type)) {
            this.messageListeners.set(type, new Set());
        }
        
        this.messageListeners.get(type).add(callback);
        
        // 返回取消注册的函数
        return () => {
            const listeners = this.messageListeners.get(type);
            if (listeners) {
                listeners.delete(callback);
            }
        };
    }

    // 触发消息事件
    emit(type, data) {
        const listeners = this.messageListeners.get(type);
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    this.errorHandler.handleError(error, `message_listener_${type}`);
                }
            });
        }
    }

    // 清理资源
    async onDestroy() {
        // 清理定时器
        if (this.stateCheckInterval) {
            clearInterval(this.stateCheckInterval);
        }
        
        if (this.connectionCheckInterval) {
            clearInterval(this.connectionCheckInterval);
        }
        
        // 清理回调
        this.stateUpdateCallbacks.clear();
        this.messageListeners.clear();
        
        console.log('事件管理器已清理');
    }

    // 公共API
    getLastStateUpdateTime() {
        return this.lastStateUpdateTime;
    }

    isPendingAutoPlay() {
        return this.pendingAutoPlay;
    }

    setPendingAutoPlay(pending) {
        this.pendingAutoPlay = pending;
    }
}
