// URL处理模块 - 处理智能URL输入、验证、历史记录等功能
import { BaseModule } from './BaseModule.js';

export class URLHandler extends BaseModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // URL相关状态
        this.currentPlayingURL = null;
        this.playbackHistory = [];
        this.MAX_HISTORY_ITEMS = 20;
        
        // DOM元素引用
        this.smartUrlInput = null;
        this.clearUrlBtn = null;
        this.urlValidationMessage = null;
        this.playingContentInfo = null;
        this.playingTitle = null;
        this.playingUrl = null;
        this.openPlayingPageBtn = null;
        
        // 历史记录相关
        this.historyHeader = null;
        this.historyToggleBtn = null;
        this.historyContent = null;
        this.historyList = null;
        this.clearHistoryBtn = null;
        
        // 状态
        this.isHistoryExpanded = false;
    }

    async onInit() {
        // 获取DOM元素
        this.initDOMElements();
        
        // 设置事件监听器
        this.setupEventListeners();
        
        // 加载播放历史
        await this.loadPlaybackHistory();
        
        // 初始化历史记录显示
        this.initializePlaybackHistory();
        
        // 渲染历史记录
        this.renderPlaybackHistory();
    }

    initDOMElements() {
        this.smartUrlInput = document.getElementById('smart-url-input');
        this.clearUrlBtn = document.getElementById('clear-url-btn');
        this.urlValidationMessage = document.getElementById('url-validation-message');
        this.playingContentInfo = document.getElementById('playing-content-info');
        this.playingTitle = document.getElementById('playing-title');
        this.playingUrl = document.getElementById('playing-url');
        this.openPlayingPageBtn = document.getElementById('open-playing-page-btn');
        
        // 历史记录元素
        this.historyHeader = document.getElementById('history-header');
        this.historyToggleBtn = document.getElementById('history-toggle-btn');
        this.historyContent = document.getElementById('history-content');
        this.historyList = document.getElementById('history-list');
        this.clearHistoryBtn = document.getElementById('clear-history-btn');
    }

    setupEventListeners() {
        // URL输入框事件
        if (this.smartUrlInput) {
            this.addEventListener(this.smartUrlInput, 'input', (e) => {
                this.handleURLInput(e.target.value);
            });
            
            this.addEventListener(this.smartUrlInput, 'keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleURLSubmit(e.target.value);
                }
            });
            
            this.addEventListener(this.smartUrlInput, 'blur', (e) => {
                this.validateURL(e.target.value);
            });
        }

        // 清除URL按钮
        if (this.clearUrlBtn) {
            this.addEventListener(this.clearUrlBtn, 'click', () => {
                this.clearURL();
            });
        }

        // 打开播放页面按钮
        if (this.openPlayingPageBtn) {
            this.addEventListener(this.openPlayingPageBtn, 'click', () => {
                this.openPlayingPage();
            });
        }

        // 历史记录切换按钮
        if (this.historyToggleBtn) {
            this.addEventListener(this.historyToggleBtn, 'click', () => {
                this.toggleHistoryVisibility();
            });
        }

        // 清除历史记录按钮
        if (this.clearHistoryBtn) {
            this.addEventListener(this.clearHistoryBtn, 'click', () => {
                this.clearPlaybackHistory();
            });
        }
    }

    // 处理URL输入
    handleURLInput(url) {
        // 实时验证URL
        if (url.trim()) {
            this.validateURL(url);
            this.showClearButton();
        } else {
            this.hideClearButton();
            this.hideURLValidationMessage();
        }
    }

    // 处理URL提交
    handleURLSubmit(url) {
        if (!url.trim()) {
            this.showURLValidationMessage('请输入有效的URL', true);
            return;
        }

        if (!this.isValidURL(url)) {
            this.showURLValidationMessage('URL格式不正确', true);
            return;
        }

        // 发送URL播放请求到后台
        this.dependencies.chrome.runtime.sendMessage({
            action: 'playURL',
            url: url.trim()
        }, (response) => {
            if (this.dependencies.chrome.runtime.lastError) {
                console.error('URL播放请求失败:', this.dependencies.chrome.runtime.lastError);
                this.showURLValidationMessage('播放请求失败', true);
            } else if (response && response.success) {
                console.log('URL播放请求成功');
                this.showURLValidationMessage('开始播放指定URL内容', false);
                
                // 添加到历史记录
                this.addToPlaybackHistory(url.trim(), '指定URL内容');
            } else {
                console.error('URL播放失败:', response);
                this.showURLValidationMessage(response?.error || '播放失败', true);
            }
        });
    }

    // 验证URL
    validateURL(url) {
        if (!url.trim()) return true;
        
        const isValid = this.isValidURL(url);
        if (isValid) {
            this.showURLValidationMessage('URL格式正确', false);
        } else {
            this.showURLValidationMessage('URL格式不正确', true);
        }
        return isValid;
    }

    // 检查URL是否有效
    isValidURL(string) {
        try {
            const url = new URL(string);
            return url.protocol === 'http:' || url.protocol === 'https:';
        } catch (_) {
            // 尝试添加协议前缀
            try {
                const urlWithProtocol = string.startsWith('http') ? string : 'https://' + string;
                const url = new URL(urlWithProtocol);
                return url.protocol === 'http:' || url.protocol === 'https:';
            } catch (_) {
                return false;
            }
        }
    }

    // 显示URL验证消息
    showURLValidationMessage(message, isError = false) {
        if (!this.urlValidationMessage) return;
        
        this.urlValidationMessage.textContent = message;
        this.urlValidationMessage.className = `url-validation-message ${isError ? 'error' : 'success'}`;
        this.urlValidationMessage.style.display = 'block';
        
        // 3秒后自动隐藏成功消息
        if (!isError) {
            setTimeout(() => {
                this.hideURLValidationMessage();
            }, 3000);
        }
    }

    // 隐藏URL验证消息
    hideURLValidationMessage() {
        if (this.urlValidationMessage) {
            this.urlValidationMessage.style.display = 'none';
        }
    }

    // 清除URL
    clearURL() {
        if (this.smartUrlInput) {
            this.smartUrlInput.value = '';
            this.smartUrlInput.disabled = false;
            this.smartUrlInput.style.opacity = '1';
            this.smartUrlInput.style.cursor = 'text';
            this.smartUrlInput.placeholder = '输入网址播放指定内容，留空播放当前页面';
        }
        
        this.hideClearButton();
        this.hideURLValidationMessage();
        this.hidePlayingContentInfo();
    }

    // 显示清除按钮
    showClearButton() {
        if (this.clearUrlBtn) {
            this.clearUrlBtn.style.display = 'block';
        }
    }

    // 隐藏清除按钮
    hideClearButton() {
        if (this.clearUrlBtn) {
            this.clearUrlBtn.style.display = 'none';
        }
    }

    // 更新播放内容信息
    updatePlayingContentInfo(title, url) {
        this.currentPlayingURL = url;
        
        if (this.playingContentInfo) {
            this.playingContentInfo.style.display = 'block';
        }
        
        if (this.playingTitle) {
            this.playingTitle.textContent = title || '未知标题';
            this.playingTitle.title = title || '未知标题';
        }
        
        if (this.playingUrl) {
            // 显示简化的URL
            const displayUrl = this.simplifyURL(url);
            this.playingUrl.textContent = displayUrl;
            this.playingUrl.title = url;
        }
        
        console.log('播放内容信息已更新:', { title, url });
    }

    // 隐藏播放内容信息
    hidePlayingContentInfo() {
        if (this.playingContentInfo) {
            this.playingContentInfo.style.display = 'none';
        }
        this.currentPlayingURL = null;
    }

    // 简化URL显示
    simplifyURL(url) {
        try {
            const urlObj = new URL(url);
            let simplified = urlObj.hostname;
            
            // 移除www前缀
            if (simplified.startsWith('www.')) {
                simplified = simplified.substring(4);
            }
            
            // 如果路径不是根路径，添加路径的一部分
            if (urlObj.pathname !== '/' && urlObj.pathname.length > 1) {
                const pathParts = urlObj.pathname.split('/').filter(part => part);
                if (pathParts.length > 0) {
                    simplified += '/' + pathParts[0];
                    if (pathParts.length > 1) {
                        simplified += '/...';
                    }
                }
            }
            
            return simplified;
        } catch (e) {
            return url.length > 50 ? url.substring(0, 47) + '...' : url;
        }
    }

    // 打开播放页面
    openPlayingPage() {
        if (this.currentPlayingURL) {
            this.dependencies.chrome.tabs.create({ url: this.currentPlayingURL });
        }
    }

    // 加载播放历史
    async loadPlaybackHistory() {
        try {
            const result = await this.dependencies.chrome.storage.local.get(['playbackHistory']);
            this.playbackHistory = result.playbackHistory || [];
            console.log('播放历史已加载:', this.playbackHistory.length, '条记录');
        } catch (error) {
            this.errorHandler.handleError(error, 'load_playback_history');
            this.playbackHistory = [];
        }
    }

    // 保存播放历史
    async savePlaybackHistory() {
        try {
            await this.dependencies.chrome.storage.local.set({
                playbackHistory: this.playbackHistory
            });
            console.log('播放历史已保存');
        } catch (error) {
            this.errorHandler.handleError(error, 'save_playback_history');
        }
    }

    // 添加到播放历史
    addToPlaybackHistory(url, title) {
        // 检查是否已存在
        const existingIndex = this.playbackHistory.findIndex(item => item.url === url);
        
        const historyItem = {
            url: url,
            title: title || '未知标题',
            timestamp: Date.now(),
            domain: this.extractDomain(url)
        };
        
        if (existingIndex !== -1) {
            // 更新现有记录
            this.playbackHistory[existingIndex] = historyItem;
        } else {
            // 添加新记录
            this.playbackHistory.unshift(historyItem);
        }
        
        // 限制历史记录数量
        if (this.playbackHistory.length > this.MAX_HISTORY_ITEMS) {
            this.playbackHistory = this.playbackHistory.slice(0, this.MAX_HISTORY_ITEMS);
        }
        
        // 保存并渲染
        this.savePlaybackHistory();
        this.renderPlaybackHistory();
    }

    // 提取域名
    extractDomain(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname.replace(/^www\./, '');
        } catch (e) {
            return '未知域名';
        }
    }

    // 渲染播放历史
    renderPlaybackHistory() {
        if (!this.historyList) return;
        
        this.historyList.innerHTML = '';
        
        if (this.playbackHistory.length === 0) {
            this.historyList.innerHTML = `
                <div class="empty-history">
                    <p>暂无播放历史</p>
                    <small>播放过的内容会显示在这里</small>
                </div>
            `;
            return;
        }
        
        this.playbackHistory.forEach((item, index) => {
            const historyItem = this.createHistoryItem(item, index);
            this.historyList.appendChild(historyItem);
        });
    }

    // 创建历史记录项
    createHistoryItem(item, index) {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        
        const timeAgo = this.getTimeAgo(item.timestamp);
        
        historyItem.innerHTML = `
            <div class="history-content">
                <div class="history-title" title="${item.title}">${item.title}</div>
                <div class="history-url" title="${item.url}">${item.domain}</div>
                <div class="history-time">${timeAgo}</div>
            </div>
            <div class="history-actions">
                <button class="play-history-btn" title="播放此内容">▶</button>
                <button class="remove-history-btn" title="删除此记录">×</button>
            </div>
        `;
        
        // 添加事件监听器
        const playBtn = historyItem.querySelector('.play-history-btn');
        const removeBtn = historyItem.querySelector('.remove-history-btn');
        
        playBtn.addEventListener('click', () => this.playHistoryItem(index));
        removeBtn.addEventListener('click', () => this.removeHistoryItem(index));
        
        return historyItem;
    }

    // 播放历史记录项
    playHistoryItem(index) {
        const item = this.playbackHistory[index];
        if (!item) return;
        
        // 设置URL输入框
        if (this.smartUrlInput) {
            this.smartUrlInput.value = item.url;
        }
        
        // 提交播放
        this.handleURLSubmit(item.url);
    }

    // 删除历史记录项
    removeHistoryItem(index) {
        this.playbackHistory.splice(index, 1);
        this.savePlaybackHistory();
        this.renderPlaybackHistory();
    }

    // 清除播放历史
    clearPlaybackHistory() {
        if (confirm('确定要清除所有播放历史吗？')) {
            this.playbackHistory = [];
            this.savePlaybackHistory();
            this.renderPlaybackHistory();
        }
    }

    // 切换历史记录可见性
    toggleHistoryVisibility() {
        this.isHistoryExpanded = !this.isHistoryExpanded;
        
        if (this.historyContent) {
            this.historyContent.style.display = this.isHistoryExpanded ? 'block' : 'none';
        }
        
        if (this.historyToggleBtn) {
            this.historyToggleBtn.textContent = this.isHistoryExpanded ? '▼' : '▶';
        }
    }

    // 初始化播放历史显示
    initializePlaybackHistory() {
        // 默认折叠历史记录
        this.isHistoryExpanded = false;
        
        if (this.historyContent) {
            this.historyContent.style.display = 'none';
        }
        
        if (this.historyToggleBtn) {
            this.historyToggleBtn.textContent = '▶';
        }
    }

    // 获取时间差描述
    getTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return new Date(timestamp).toLocaleDateString();
    }

    // 公共API
    getCurrentPlayingURL() {
        return this.currentPlayingURL;
    }

    getPlaybackHistory() {
        return [...this.playbackHistory];
    }

    setCurrentURL(url) {
        if (this.smartUrlInput) {
            this.smartUrlInput.value = url || '';
        }
        
        if (url) {
            this.showClearButton();
        } else {
            this.hideClearButton();
        }
    }
}
