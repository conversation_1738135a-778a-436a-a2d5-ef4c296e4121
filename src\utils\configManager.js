// 配置管理器 - 统一管理应用配置
class ConfigManager {
    constructor() {
        this.config = new Map();
        this.loadDefaultConfig();
    }

    // 加载默认配置
    loadDefaultConfig() {
        const defaultConfig = {
            // TTS相关配置
            tts: {
                defaultSpeed: 1.1,
                speedRange: { min: 0.5, max: 5.0 },
                speedStep: 0.1,
                chunkSize: 200,
                maxRetries: 3,
                retryDelay: 1000
            },

            // 连续阅读配置
            continuousReading: {
                defaultChapters: 30,
                maxChapters: 999,
                minChapters: 1,
                defaultDuration: 30 * 60, // 30分钟（秒）
                maxDuration: 12 * 60 * 60 // 12小时（秒）
            },

            // UI更新配置
            ui: {
                stateUpdateInterval: 5000, // 5秒
                debounceDelay: 300, // 300毫秒
                animationDuration: 300, // 300毫秒
                toastDuration: 2000, // 2秒
                maxHistoryItems: 20
            },

            // 性能配置
            performance: {
                maxConcurrentRequests: 3,
                requestTimeout: 10000, // 10秒
                cacheExpiry: 5 * 60 * 1000, // 5分钟
                maxCacheSize: 100
            },

            // 错误处理配置
            errorHandling: {
                maxRetries: 3,
                retryDelay: 1000,
                timeoutDuration: 30000, // 30秒
                logLevel: 'info' // 'debug', 'info', 'warn', 'error'
            },

            // 存储配置
            storage: {
                keys: {
                    favoriteVoices: 'favoriteVoices',
                    currentVoice: 'currentVoice',
                    playbackHistory: 'playbackHistory',
                    userSettings: 'userSettings',
                    voiceFilters: 'voiceFilters'
                },
                syncInterval: 1000 // 1秒
            },

            // 网络配置
            network: {
                connectionTimeout: 5000, // 5秒
                maxRetries: 3,
                retryDelay: 2000 // 2秒
            },

            // 调试配置
            debug: {
                enableLogging: true,
                enablePerformanceMonitoring: false,
                enableStateLogging: false
            }
        };

        // 将配置存储到Map中
        this.setConfig(defaultConfig);
    }

    // 设置配置
    setConfig(configObj, prefix = '') {
        Object.entries(configObj).forEach(([key, value]) => {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                // 递归处理嵌套对象
                this.setConfig(value, fullKey);
            } else {
                this.config.set(fullKey, value);
            }
        });
    }

    // 获取配置值
    get(key, defaultValue = null) {
        return this.config.get(key) ?? defaultValue;
    }

    // 设置单个配置值
    set(key, value) {
        this.config.set(key, value);
    }

    // 获取配置分组
    getGroup(groupKey) {
        const group = {};
        const prefix = `${groupKey}.`;
        
        this.config.forEach((value, key) => {
            if (key.startsWith(prefix)) {
                const subKey = key.substring(prefix.length);
                group[subKey] = value;
            }
        });
        
        return group;
    }

    // 更新配置分组
    updateGroup(groupKey, updates) {
        Object.entries(updates).forEach(([key, value]) => {
            this.set(`${groupKey}.${key}`, value);
        });
    }

    // 验证配置值
    validate(key, value) {
        const validationRules = {
            'tts.defaultSpeed': (v) => v >= 0.5 && v <= 5.0,
            'tts.chunkSize': (v) => v > 0 && v <= 1000,
            'continuousReading.defaultChapters': (v) => v >= 1 && v <= 999,
            'ui.stateUpdateInterval': (v) => v >= 1000 && v <= 60000,
            'performance.maxConcurrentRequests': (v) => v >= 1 && v <= 10
        };

        const validator = validationRules[key];
        if (validator && !validator(value)) {
            throw new Error(`配置值 ${key} 验证失败: ${value}`);
        }

        return true;
    }

    // 安全设置配置（带验证）
    setSafe(key, value) {
        this.validate(key, value);
        this.set(key, value);
    }

    // 从存储加载用户配置
    async loadUserConfig() {
        try {
            const result = await chrome.storage.local.get(['userSettings']);
            if (result.userSettings) {
                this.setConfig(result.userSettings, 'user');
                console.log('用户配置已加载');
            }
        } catch (error) {
            console.error('加载用户配置失败:', error);
        }
    }

    // 保存用户配置到存储
    async saveUserConfig() {
        try {
            const userConfig = {};
            this.config.forEach((value, key) => {
                if (key.startsWith('user.')) {
                    const userKey = key.substring(5);
                    userConfig[userKey] = value;
                }
            });

            await chrome.storage.local.set({ userSettings: userConfig });
            console.log('用户配置已保存');
        } catch (error) {
            console.error('保存用户配置失败:', error);
        }
    }

    // 重置为默认配置
    reset() {
        this.config.clear();
        this.loadDefaultConfig();
    }

    // 获取所有配置（调试用）
    getAllConfig() {
        const result = {};
        this.config.forEach((value, key) => {
            result[key] = value;
        });
        return result;
    }

    // 获取配置统计信息
    getStats() {
        return {
            totalKeys: this.config.size,
            groups: this.getConfigGroups()
        };
    }

    // 获取配置分组列表
    getConfigGroups() {
        const groups = new Set();
        this.config.forEach((value, key) => {
            const parts = key.split('.');
            if (parts.length > 1) {
                groups.add(parts[0]);
            }
        });
        return Array.from(groups);
    }
}

// 创建全局配置管理器实例
export const configManager = new ConfigManager();

// 便捷的配置访问函数
export const getConfig = (key, defaultValue) => configManager.get(key, defaultValue);
export const setConfig = (key, value) => configManager.setSafe(key, value);
export const getConfigGroup = (groupKey) => configManager.getGroup(groupKey);

// 初始化时加载用户配置
configManager.loadUserConfig();

console.log("配置管理器已初始化");
