// 最简单的Service Worker测试版本
console.log("🚀 简单测试Service Worker启动");

// 基础API检查
console.log("Chrome API可用:", typeof chrome !== 'undefined');
console.log("Action API可用:", typeof chrome?.action !== 'undefined');
console.log("SidePanel API可用:", typeof chrome?.sidePanel !== 'undefined');

// 设置插件图标点击处理器
if (typeof chrome !== 'undefined' && chrome.action) {
  chrome.action.onClicked.addListener((tab) => {
    console.log("🖱️ 插件图标被点击！");
    console.log("标签页信息:", tab);
    
    // 尝试打开侧边栏
    if (chrome.sidePanel) {
      chrome.sidePanel.open({ windowId: tab.windowId })
        .then(() => {
          console.log("✅ 侧边栏打开成功");
        })
        .catch((error) => {
          console.error("❌ 侧边栏打开失败:", error);
          alert("侧边栏打开失败: " + error.message);
        });
    } else {
      console.error("❌ SidePanel API不可用");
      alert("浏览器不支持侧边栏功能");
    }
  });
  
  console.log("✅ 插件图标点击处理器设置完成");
} else {
  console.error("❌ Chrome Action API不可用");
}

// 监听消息
if (typeof chrome !== 'undefined' && chrome.runtime) {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("📨 收到消息:", message);
    sendResponse({ status: "测试响应" });
    return true;
  });
  
  console.log("✅ 消息监听器设置完成");
} else {
  console.error("❌ Chrome Runtime API不可用");
}

console.log("🎯 简单测试Service Worker初始化完成");
