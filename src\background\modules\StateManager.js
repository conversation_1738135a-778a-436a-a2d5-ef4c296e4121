// 状态管理模块 - 处理应用状态管理、持久化、同步等功能
import { BaseBackgroundModule } from './BaseBackgroundModule.js';

export class StateManager extends BaseBackgroundModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // 应用状态
        this.currentState = {
            readingState: 'idle', // idle, loading, reading, paused, stopped, error
            currentReadingTabId: null,
            currentChapterCount: 0,
            currentSpeed: 1.1,
            currentVoice: null,
            continuousReading: true,
            continuousChapters: 30,
            readingDuration: 30 * 60, // 30分钟
            currentArticle: null,
            currentChapterTitle: null,
            currentPlayingURL: null,
            error: null,
            lastUpdated: Date.now()
        };
        
        // 状态历史（用于调试和回滚）
        this.stateHistory = [];
        this.maxHistorySize = 50;
        
        // 状态变化监听器
        this.stateChangeListeners = new Set();
        
        // 状态持久化配置
        this.persistentKeys = [
            'currentSpeed',
            'currentVoice', 
            'continuousReading',
            'continuousChapters',
            'readingDuration'
        ];
        
        // 状态同步定时器
        this.syncTimer = null;
        this.syncInterval = 5000; // 5秒同步一次
    }

    async onInit() {
        // 加载持久化状态
        await this.loadPersistedState();
        
        // 设置消息监听器
        this.setupMessageListeners();
        
        // 启动状态同步
        this.startStateSync();
        
        // 设置状态变化监听
        this.setupStateChangeHandlers();
    }

    // 设置消息监听器
    setupMessageListeners() {
        // 获取当前状态
        this.addMessageListener('getState', (message, sender, sendResponse) => {
            this.handleGetState(message, sendResponse);
        });

        // 更新状态
        this.addMessageListener('updateState', (message, sender, sendResponse) => {
            this.handleUpdateState(message, sendResponse);
        });

        // 重置状态
        this.addMessageListener('resetState', (message, sender, sendResponse) => {
            this.handleResetState(message, sendResponse);
        });

        // 获取状态历史
        this.addMessageListener('getStateHistory', (message, sender, sendResponse) => {
            this.handleGetStateHistory(message, sendResponse);
        });

        // 回滚状态
        this.addMessageListener('rollbackState', (message, sender, sendResponse) => {
            this.handleRollbackState(message, sendResponse);
        });
    }

    // 处理获取状态
    handleGetState(message, sendResponse) {
        try {
            const state = this.getCurrentState();
            sendResponse({ 
                success: true, 
                state: state,
                timestamp: Date.now()
            });
        } catch (error) {
            this.error("获取状态失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理更新状态
    handleUpdateState(message, sendResponse) {
        try {
            const { updates, source } = message;
            
            if (!updates || typeof updates !== 'object') {
                throw new Error('状态更新参数无效');
            }

            this.updateState(updates, source);
            
            sendResponse({ 
                success: true, 
                state: this.getCurrentState(),
                updated: Object.keys(updates)
            });
        } catch (error) {
            this.error("更新状态失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理重置状态
    handleResetState(message, sendResponse) {
        try {
            const { keepPersistent = true } = message;
            
            this.resetState(keepPersistent);
            
            sendResponse({ 
                success: true, 
                state: this.getCurrentState(),
                message: '状态已重置'
            });
        } catch (error) {
            this.error("重置状态失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理获取状态历史
    handleGetStateHistory(message, sendResponse) {
        try {
            const { limit = 10 } = message;
            
            const history = this.stateHistory
                .slice(-limit)
                .map(entry => ({
                    ...entry,
                    state: { ...entry.state } // 深拷贝状态
                }));
            
            sendResponse({ 
                success: true, 
                history: history,
                total: this.stateHistory.length
            });
        } catch (error) {
            this.error("获取状态历史失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理回滚状态
    handleRollbackState(message, sendResponse) {
        try {
            const { steps = 1 } = message;
            
            if (this.stateHistory.length < steps) {
                throw new Error('历史记录不足，无法回滚');
            }

            const targetState = this.stateHistory[this.stateHistory.length - steps - 1];
            if (!targetState) {
                throw new Error('目标状态不存在');
            }

            // 回滚到目标状态
            this.currentState = { ...targetState.state };
            this.currentState.lastUpdated = Date.now();
            
            // 移除回滚后的历史记录
            this.stateHistory = this.stateHistory.slice(0, -steps);
            
            // 通知状态变化
            this.notifyStateChange('rollback');
            
            this.log(`状态已回滚 ${steps} 步`);
            
            sendResponse({ 
                success: true, 
                state: this.getCurrentState(),
                rolledBack: steps
            });
        } catch (error) {
            this.error("回滚状态失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 更新状态
    updateState(updates, source = 'unknown') {
        // 保存当前状态到历史
        this.saveStateToHistory(source);
        
        // 验证更新
        const validatedUpdates = this.validateStateUpdates(updates);
        
        // 应用更新
        Object.assign(this.currentState, validatedUpdates);
        this.currentState.lastUpdated = Date.now();
        
        // 持久化需要保存的状态
        this.persistState();
        
        // 通知状态变化
        this.notifyStateChange(source, validatedUpdates);
        
        this.log(`状态已更新 (来源: ${source}):`, Object.keys(validatedUpdates));
    }

    // 验证状态更新
    validateStateUpdates(updates) {
        const validated = {};
        
        for (const [key, value] of Object.entries(updates)) {
            switch (key) {
                case 'readingState':
                    if (['idle', 'loading', 'reading', 'paused', 'stopped', 'error', 'navigating'].includes(value)) {
                        validated[key] = value;
                    }
                    break;
                    
                case 'currentSpeed':
                    if (typeof value === 'number' && value >= 0.1 && value <= 3.0) {
                        validated[key] = value;
                    }
                    break;
                    
                case 'continuousChapters':
                    if (typeof value === 'number' && value >= 1 && value <= 100) {
                        validated[key] = value;
                    }
                    break;
                    
                case 'readingDuration':
                    if (typeof value === 'number' && value >= 60 && value <= 12 * 60 * 60) {
                        validated[key] = value;
                    }
                    break;
                    
                case 'continuousReading':
                    if (typeof value === 'boolean') {
                        validated[key] = value;
                    }
                    break;
                    
                case 'currentReadingTabId':
                case 'currentChapterCount':
                case 'currentVoice':
                case 'currentArticle':
                case 'currentChapterTitle':
                case 'currentPlayingURL':
                case 'error':
                    validated[key] = value;
                    break;
                    
                default:
                    this.warn(`未知的状态键: ${key}`);
            }
        }
        
        return validated;
    }

    // 重置状态
    resetState(keepPersistent = true) {
        const persistentState = {};
        
        if (keepPersistent) {
            // 保留持久化状态
            this.persistentKeys.forEach(key => {
                if (this.currentState[key] !== undefined) {
                    persistentState[key] = this.currentState[key];
                }
            });
        }
        
        // 重置为默认状态
        this.currentState = {
            readingState: 'idle',
            currentReadingTabId: null,
            currentChapterCount: 0,
            currentSpeed: 1.1,
            currentVoice: null,
            continuousReading: true,
            continuousChapters: 30,
            readingDuration: 30 * 60,
            currentArticle: null,
            currentChapterTitle: null,
            currentPlayingURL: null,
            error: null,
            lastUpdated: Date.now(),
            ...persistentState
        };
        
        // 清理历史记录
        this.stateHistory = [];
        
        // 通知状态变化
        this.notifyStateChange('reset');
        
        this.log("状态已重置");
    }

    // 保存状态到历史
    saveStateToHistory(source) {
        const historyEntry = {
            state: { ...this.currentState },
            source: source,
            timestamp: Date.now()
        };
        
        this.stateHistory.push(historyEntry);
        
        // 限制历史记录大小
        if (this.stateHistory.length > this.maxHistorySize) {
            this.stateHistory = this.stateHistory.slice(-this.maxHistorySize);
        }
    }

    // 持久化状态
    async persistState() {
        try {
            const persistentData = {};
            
            this.persistentKeys.forEach(key => {
                if (this.currentState[key] !== undefined) {
                    persistentData[key] = this.currentState[key];
                }
            });
            
            await this.setStorage('persistentState', persistentData);
            
        } catch (error) {
            this.error("持久化状态失败:", error);
        }
    }

    // 加载持久化状态
    async loadPersistedState() {
        try {
            const persistentData = await this.getStorage('persistentState', {});
            
            if (persistentData && typeof persistentData === 'object') {
                Object.assign(this.currentState, persistentData);
                this.log("已加载持久化状态:", Object.keys(persistentData));
            }
            
        } catch (error) {
            this.error("加载持久化状态失败:", error);
        }
    }

    // 通知状态变化
    notifyStateChange(source, updates = null) {
        const changeEvent = {
            state: { ...this.currentState },
            source: source,
            updates: updates,
            timestamp: Date.now()
        };
        
        // 通知内部监听器
        this.stateChangeListeners.forEach(listener => {
            try {
                listener(changeEvent);
            } catch (error) {
                this.error("状态变化监听器错误:", error);
            }
        });
        
        // 通知其他模块
        if (this.dependencies.moduleContainer) {
            this.dependencies.moduleContainer.emit('stateChange', changeEvent);
        }
        
        // 发送到前端
        this.sendStateUpdate(this.currentState);
    }

    // 设置状态变化处理器
    setupStateChangeHandlers() {
        this.onStateChange((event) => {
            const { state, source, updates } = event;
            
            // 处理特定状态变化
            if (updates && updates.readingState) {
                this.handleReadingStateChange(updates.readingState, state);
            }
            
            if (updates && updates.currentVoice) {
                this.handleVoiceChange(updates.currentVoice, state);
            }
        });
    }

    // 处理阅读状态变化
    handleReadingStateChange(newState, fullState) {
        this.log(`阅读状态变化: ${newState}`);
        
        switch (newState) {
            case 'reading':
                // 开始播放时的处理
                break;
                
            case 'paused':
                // 暂停时的处理
                break;
                
            case 'stopped':
                // 停止时的处理
                break;
                
            case 'error':
                // 错误时的处理
                this.error("阅读状态错误:", fullState.error);
                break;
        }
    }

    // 处理语音变化
    handleVoiceChange(newVoice, fullState) {
        this.log("语音已变化:", newVoice?.name || 'null');
    }

    // 启动状态同步
    startStateSync() {
        this.syncTimer = this.createTimer(() => {
            this.syncStateToUI();
        }, this.syncInterval, false);
        
        this.log("状态同步已启动");
    }

    // 同步状态到UI
    syncStateToUI() {
        this.sendStateUpdate(this.currentState);
    }

    // 添加状态变化监听器
    onStateChange(listener) {
        this.stateChangeListeners.add(listener);
        
        return () => {
            this.stateChangeListeners.delete(listener);
        };
    }

    // 获取当前状态
    getCurrentState() {
        return { ...this.currentState };
    }

    // 获取特定状态值
    getStateValue(key) {
        return this.currentState[key];
    }

    // 设置特定状态值
    setStateValue(key, value, source = 'direct') {
        this.updateState({ [key]: value }, source);
    }

    // 检查状态是否为指定值
    isState(key, value) {
        return this.currentState[key] === value;
    }

    // 获取状态
    getStatus() {
        return {
            ...super.getStatus(),
            currentState: this.currentState.readingState,
            historySize: this.stateHistory.length,
            stateChangeListeners: this.stateChangeListeners.size,
            lastUpdated: this.currentState.lastUpdated,
            syncInterval: this.syncInterval
        };
    }

    // 清理资源
    async onDestroy() {
        // 清理定时器
        if (this.syncTimer) {
            this.clearTimer(this.syncTimer);
        }
        
        // 清理监听器
        this.stateChangeListeners.clear();
        
        // 最后一次持久化
        await this.persistState();
        
        await super.onDestroy();
    }

    // 公共API
    getReadingState() {
        return this.currentState.readingState;
    }

    setReadingState(state, source = 'api') {
        this.updateState({ readingState: state }, source);
    }

    getCurrentTabId() {
        return this.currentState.currentReadingTabId;
    }

    setCurrentTabId(tabId, source = 'api') {
        this.updateState({ currentReadingTabId: tabId }, source);
    }

    getCurrentVoice() {
        return this.currentState.currentVoice;
    }

    setCurrentVoice(voice, source = 'api') {
        this.updateState({ currentVoice: voice }, source);
    }

    getSpeed() {
        return this.currentState.currentSpeed;
    }

    setSpeed(speed, source = 'api') {
        this.updateState({ currentSpeed: speed }, source);
    }

    isContinuousReading() {
        return this.currentState.continuousReading;
    }

    setContinuousReading(enabled, source = 'api') {
        this.updateState({ continuousReading: enabled }, source);
    }
}
