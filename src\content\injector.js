// Content Script Injector for 神灯 AI 朗读增强助手

console.log("Content script injected.");

// --- Core Logic Imports (Placeholder - requires build step or modules) ---
// import { parsePageContent, findNextPageLink } from './parser.js'; 

// --- DOM Interaction ---

function highlightText(textNode, start, end) {
    // TODO: Implement text highlighting logic
    console.log("Highlighting:", textNode, start, end);
    // Might involve wrapping text in a span with a specific class
}

function getPageTextContent() {
    // TODO: Implement robust content extraction logic
    // This needs to be smart enough to find the main article/story body
    // Could start simple: document.body.innerText
    // Later: Use heuristics, selectors, or libraries (like Readability.js packaged with the extension)
    console.log("Extracting page content (placeholder)");
    // Example: Look for common article containers
    const mainContentSelectors = ['article', 'main', '.post-content', '.entry-content', '#content'];
    let mainElement = null;
    for (const selector of mainContentSelectors) {
        mainElement = document.querySelector(selector);
        if (mainElement) break;
    }
    return mainElement ? mainElement.innerText : document.body.innerText;
}

function findNextChapterLinkElement() {
    // TODO: Implement logic to find the 'next chapter' or 'next page' link
    console.log("Finding next chapter link (placeholder)");
    const linkTexts = ['下一页', '下一章', 'Next', 'next', '下一節']; // Common link texts
    const links = Array.from(document.querySelectorAll('a'));
    
    for (const text of linkTexts) {
        const foundLink = links.find(a => a.innerText.trim().toLowerCase() === text.toLowerCase());
        if (foundLink) {
            console.log("Found potential next link:", foundLink);
            return foundLink;
        }
    }
     console.log("Next chapter link not found with basic search.");
    return null;
}

// --- Communication with Service Worker ---

// Listen for messages from the Service Worker or UI (e.g., to start extraction)
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("Message received in content script:", message);
    
    if (message.action === "requestContentExtraction") {
        const content = getPageTextContent();
        console.log("Sending extracted content to background.");
        sendResponse({ data: content });
    } else if (message.action === "findNextLink") {
        const linkElement = findNextChapterLinkElement();
        const href = linkElement ? linkElement.href : null;
         sendResponse({ nextLink: href });
    } else if (message.action === "clickNextLink") {
         const linkElement = findNextChapterLinkElement();
         if (linkElement) {
             console.log("Clicking next link:", linkElement);
             linkElement.click();
             sendResponse({ status: "clicked" });
         } else {
             sendResponse({ status: "not_found" });
         }
    } else if (message.action === "highlight") {
        // TODO: Receive specific text/range to highlight from background
        console.log("Highlight request received (placeholder)", message.data);
        // highlightText(...); 
        sendResponse({ status: "highlight processed (placeholder)" });
    }
    
    // Keep the message channel open for asynchronous response if needed
    // return true; 
});

// --- Initial Setup ---

// Example: Send a message to the background script that the content script is ready
// chrome.runtime.sendMessage({ action: "contentScriptReady", url: window.location.href });

console.log("Content script setup complete.");

// Note: For more complex DOM manipulation or if using frameworks like React/Vue
// to inject UI elements into the page, a more sophisticated setup is needed,
// likely involving a build process (Webpack, Vite) to bundle dependencies.
