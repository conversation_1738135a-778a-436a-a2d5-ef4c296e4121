// 播放控制模块 - 处理播放、暂停、停止、速度控制等功能
import { BaseModule } from './BaseModule.js';

export class PlaybackController extends BaseModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // 播放状态
        this.currentState = {
            readingState: 'idle',
            currentSpeed: 1.1,
            currentVoice: null,
            continuousReading: true,
            article: null,
            error: null,
            continuousChapters: 30,
            readingDuration: 30 * 60
        };
        
        // 播放计时器相关
        this.playbackTimer = null;
        this.playbackStartTime = 0;
        this.playbackElapsedTime = 0;
        this.totalPlaybackTime = 0;
        this.remainingTime = 0;
        
        // DOM元素引用
        this.playPauseButton = null;
        this.stopBtn = null;
        this.speedSlider = null;
        this.speedValueEl = null;
        this.speedDecreaseBtn = null;
        this.speedIncreaseBtn = null;
        this.speedDisplay = null;
        this.playbackTimerEl = null;
        this.statusIndicatorEl = null;
        this.playIcon = null;
        this.pauseIcon = null;
        
        // 进度条相关
        this.progressBarContainer = null;
        this.progressBar = null;
        this.currentTimeEl = null;
        this.totalTimeEl = null;
        this.timeBubbleEl = null;
        this.isDraggingDuration = false;
        
        // 连续阅读相关
        this.continuousReadingToggle = null;
        this.chapterInput = null;
        this.decrementChapterBtn = null;
        this.incrementChapterBtn = null;
        this.chaptersDisplay = null;
        
        // 常量
        this.MAX_DURATION_SECONDS = 12 * 60 * 60; // 12小时
    }

    async onInit() {
        // 获取DOM元素
        this.initDOMElements();
        
        // 设置事件监听器
        this.setupEventListeners();
        
        // 初始化控制面板值
        this.initializeControlPanelValues();
        
        // 启动状态同步
        this.startStateSynchronization();
    }

    initDOMElements() {
        this.playPauseButton = document.getElementById('play-pause-btn');
        this.stopBtn = document.getElementById('stop-btn');
        this.speedSlider = document.getElementById('speed-slider');
        this.speedValueEl = document.getElementById('speed-value');
        this.speedDecreaseBtn = document.getElementById('speed-decrease-btn');
        this.speedIncreaseBtn = document.getElementById('speed-increase-btn');
        this.speedDisplay = document.getElementById('speed-display');
        this.playbackTimerEl = document.getElementById('playback-timer');
        this.statusIndicatorEl = document.getElementById('status-indicator');
        
        if (this.playPauseButton) {
            this.playIcon = this.playPauseButton.querySelector('.icon-play');
            this.pauseIcon = this.playPauseButton.querySelector('.icon-pause');
        }
        
        // 进度条元素
        this.progressBarContainer = document.querySelector('.progress-bar-container');
        this.progressBar = document.getElementById('progress-bar');
        this.currentTimeEl = document.getElementById('current-time');
        this.totalTimeEl = document.getElementById('total-time');
        this.timeBubbleEl = document.getElementById('time-bubble');
        
        // 连续阅读元素
        this.continuousReadingToggle = document.getElementById('continuous-reading-toggle');
        this.chapterInput = document.getElementById('continuous-read-chapters');
        this.decrementChapterBtn = document.getElementById('decrement-chapter-btn');
        this.incrementChapterBtn = document.getElementById('increment-chapter-btn');
        this.chaptersDisplay = document.getElementById('chapters-display');
    }

    setupEventListeners() {
        // 播放/暂停按钮
        if (this.playPauseButton) {
            this.addEventListener(this.playPauseButton, 'click', () => {
                this.handlePlayPauseClick();
            });
        }

        // 停止按钮
        if (this.stopBtn) {
            this.addEventListener(this.stopBtn, 'click', () => {
                this.handleStopClick();
            });
        }

        // 速度控制
        if (this.speedSlider) {
            this.addEventListener(this.speedSlider, 'input', (e) => {
                this.updateSpeed(parseFloat(e.target.value));
            });
        }

        if (this.speedDecreaseBtn) {
            this.addEventListener(this.speedDecreaseBtn, 'click', () => {
                this.adjustSpeed(-0.1);
            });
        }

        if (this.speedIncreaseBtn) {
            this.addEventListener(this.speedIncreaseBtn, 'click', () => {
                this.adjustSpeed(0.1);
            });
        }

        // 连续阅读控制
        if (this.continuousReadingToggle) {
            this.addEventListener(this.continuousReadingToggle, 'change', (e) => {
                this.handleContinuousReadingToggle(e.target.checked);
            });
        }

        if (this.decrementChapterBtn) {
            this.addEventListener(this.decrementChapterBtn, 'click', () => {
                this.adjustChapterCount(-1);
            });
        }

        if (this.incrementChapterBtn) {
            this.addEventListener(this.incrementChapterBtn, 'click', () => {
                this.adjustChapterCount(1);
            });
        }

        if (this.chapterInput) {
            this.addEventListener(this.chapterInput, 'change', (e) => {
                this.handleChapterInputChange(parseInt(e.target.value));
            });
        }

        // 进度条拖拽
        this.setupProgressBarEvents();
    }

    setupProgressBarEvents() {
        if (!this.progressBarContainer) return;

        this.addEventListener(this.progressBarContainer, 'pointerdown', (e) => {
            this.handleDurationPointerEvent(e);
        });

        this.addEventListener(document, 'pointermove', (e) => {
            this.onPointerMove(e);
        });

        this.addEventListener(document, 'pointerup', (e) => {
            this.onPointerUp(e);
        });
    }

    // 处理播放/暂停点击
    handlePlayPauseClick() {
        console.log("播放/暂停按钮被点击，当前状态:", this.currentState.readingState);

        if (this.currentState.readingState === 'reading') {
            // 当前正在播放，执行暂停
            this.pauseReading();
        } else if (this.currentState.readingState === 'paused') {
            // 当前暂停，恢复播放
            this.resumeReading();
        } else if (this.currentState.readingState === 'stopped') {
            // 从停止状态重新开始播放
            this.startReadingFromBeginning();
        } else {
            // 从空闲状态开始播放
            this.startReadingFromBeginning();
        }
    }

    // 处理停止点击
    handleStopClick() {
        console.log("🛑 停止按钮被点击");
        
        // 发送停止消息到后台
        this.dependencies.chrome.runtime.sendMessage({ action: 'stopReading' }, (response) => {
            if (this.dependencies.chrome.runtime.lastError) {
                console.error("停止朗读时出错:", this.dependencies.chrome.runtime.lastError);
                this.showMessage(`停止朗读时出错: ${this.dependencies.chrome.runtime.lastError.message}`, true);
            } else {
                console.log("🛑 停止朗读响应:", response);
                
                if (response && response.status === "stopped") {
                    console.log("✅ 后台确认停止成功");
                    this.updateStateAfterStop();
                } else if (response && response.status === "no_action") {
                    console.log("ℹ️ 后台报告无需停止操作");
                    this.showMessage("当前没有播放中的内容", false);
                }
                
                // 确保计时器被重置
                this.resetPlaybackTimer();
                
                // 请求状态更新
                setTimeout(() => {
                    this.requestStateUpdate();
                }, 300);
            }
        });
    }

    // 暂停阅读
    pauseReading() {
        this.dependencies.chrome.runtime.sendMessage({ action: 'pauseReading' }, (response) => {
            if (this.dependencies.chrome.runtime.lastError) {
                console.error("暂停朗读时出错:", this.dependencies.chrome.runtime.lastError);
            } else {
                console.log("⏸️ 暂停朗读响应:", response);
                this.pausePlaybackTimer();
            }
        });
    }

    // 恢复阅读
    resumeReading() {
        this.dependencies.chrome.runtime.sendMessage({ action: 'resumeReading' }, (response) => {
            if (this.dependencies.chrome.runtime.lastError) {
                console.error("恢复朗读时出错:", this.dependencies.chrome.runtime.lastError);
            } else {
                console.log("▶️ 恢复朗读响应:", response);
                this.resumePlaybackTimer();
            }
        });
    }

    // 从头开始阅读
    startReadingFromBeginning() {
        console.log("🚀 开始从头播放");
        
        this.dependencies.chrome.runtime.sendMessage({ action: 'startReading' }, (response) => {
            if (this.dependencies.chrome.runtime.lastError) {
                console.error("开始朗读时出错:", this.dependencies.chrome.runtime.lastError);
                this.showMessage(`开始朗读时出错: ${this.dependencies.chrome.runtime.lastError.message}`, true);
            } else {
                console.log("🚀 开始朗读响应:", response);
                if (response && response.success) {
                    this.startPlaybackTimer();
                }
            }
        });
    }

    // 更新速度
    updateSpeed(newSpeed) {
        // 限制速度范围
        newSpeed = Math.max(0.1, Math.min(3.0, newSpeed));
        
        this.currentState.currentSpeed = newSpeed;
        
        // 更新UI
        if (this.speedSlider) {
            this.speedSlider.value = newSpeed;
        }
        if (this.speedValueEl) {
            this.speedValueEl.textContent = newSpeed.toFixed(1) + 'x';
        }
        if (this.speedDisplay) {
            this.speedDisplay.textContent = newSpeed.toFixed(1) + 'x';
        }
        
        // 发送到后台
        this.dependencies.chrome.runtime.sendMessage({
            action: 'setSpeed',
            speed: newSpeed
        });
        
        console.log("速度已更新为:", newSpeed);
    }

    // 调整速度
    adjustSpeed(delta) {
        const newSpeed = this.currentState.currentSpeed + delta;
        this.updateSpeed(newSpeed);
    }

    // 处理连续阅读切换
    handleContinuousReadingToggle(enabled) {
        this.currentState.continuousReading = enabled;
        
        this.dependencies.chrome.runtime.sendMessage({
            action: 'setContinuousReading',
            enabled: enabled
        });
        
        console.log("连续阅读已", enabled ? "启用" : "禁用");
    }

    // 调整章节数量
    adjustChapterCount(delta) {
        const newCount = Math.max(1, Math.min(100, this.currentState.continuousChapters + delta));
        this.updateChapterCount(newCount);
    }

    // 处理章节输入变化
    handleChapterInputChange(value) {
        if (isNaN(value) || value < 1 || value > 100) {
            this.chapterInput.value = this.currentState.continuousChapters;
            return;
        }
        this.updateChapterCount(value);
    }

    // 更新章节数量
    updateChapterCount(count) {
        this.currentState.continuousChapters = count;
        
        if (this.chapterInput) {
            this.chapterInput.value = count;
        }
        if (this.chaptersDisplay) {
            this.chaptersDisplay.textContent = count;
        }
        
        this.dependencies.chrome.runtime.sendMessage({
            action: 'setContinuousChapters',
            chapters: count
        });
    }

    // 播放计时器相关方法
    startPlaybackTimer() {
        if (this.playbackTimer) {
            clearInterval(this.playbackTimer);
        }
        
        this.playbackStartTime = Date.now() - this.playbackElapsedTime * 1000;
        
        this.playbackTimer = setInterval(() => {
            this.updatePlaybackTimer();
        }, 1000);
        
        console.log("播放计时器已启动");
    }

    pausePlaybackTimer() {
        if (this.playbackTimer) {
            clearInterval(this.playbackTimer);
            this.playbackTimer = null;
        }
        
        this.playbackElapsedTime = (Date.now() - this.playbackStartTime) / 1000;
        console.log("播放计时器已暂停，已播放时间:", this.playbackElapsedTime);
    }

    resumePlaybackTimer() {
        this.startPlaybackTimer();
        console.log("播放计时器已恢复");
    }

    resetPlaybackTimer() {
        if (this.playbackTimer) {
            clearInterval(this.playbackTimer);
            this.playbackTimer = null;
        }
        
        this.playbackStartTime = 0;
        this.playbackElapsedTime = 0;
        this.totalPlaybackTime = 0;
        this.remainingTime = 0;
        
        this.updatePlaybackTimerDisplay();
        console.log("播放计时器已重置");
    }

    updatePlaybackTimer() {
        if (this.playbackStartTime > 0) {
            this.playbackElapsedTime = (Date.now() - this.playbackStartTime) / 1000;
            this.totalPlaybackTime = this.playbackElapsedTime;
            
            // 计算剩余时间
            if (this.currentState.readingDuration > 0) {
                this.remainingTime = Math.max(0, this.currentState.readingDuration - this.totalPlaybackTime);
            }
            
            this.updatePlaybackTimerDisplay();
        }
    }

    updatePlaybackTimerDisplay() {
        if (!this.playbackTimerEl) return;
        
        const elapsed = this.formatPlaybackTime(this.totalPlaybackTime);
        const remaining = this.formatPlaybackTime(this.remainingTime);
        
        this.playbackTimerEl.textContent = `${elapsed} / ${remaining}`;
    }

    formatPlaybackTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    // 进度条相关方法
    handleDurationPointerEvent(event) {
        if (event.button !== 0) return; // 只处理左键
        
        this.isDraggingDuration = true;
        this.progressBarContainer.classList.add('dragging');
        this.progressBarContainer.setPointerCapture(event.pointerId);
        
        this.updateDurationFromEvent(event);
    }

    onPointerMove(event) {
        if (!this.isDraggingDuration) return;
        this.updateDurationFromEvent(event);
    }

    onPointerUp(event) {
        if (!this.isDraggingDuration) return;
        
        this.isDraggingDuration = false;
        this.progressBarContainer.classList.remove('dragging');
        this.progressBarContainer.releasePointerCapture(event.pointerId);
    }

    updateDurationFromEvent(event) {
        const rect = this.progressBarContainer.getBoundingClientRect();
        const percentage = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width));
        this.updateDurationUI(percentage);
    }

    updateDurationUI(percentage) {
        const newDuration = percentage * this.MAX_DURATION_SECONDS;
        this.currentState.readingDuration = newDuration;
        
        // 更新进度条
        if (this.progressBar) {
            this.progressBar.style.width = `${percentage * 100}%`;
        }
        
        // 更新时间显示
        const formattedTime = this.formatTime(newDuration);
        if (this.totalTimeEl) {
            this.totalTimeEl.textContent = formattedTime;
        }
        
        // 发送到后台
        this.dependencies.chrome.runtime.sendMessage({
            action: 'setReadingDuration',
            payload: newDuration
        });
    }

    formatTime(totalSeconds) {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        return hours > 0 ? `${hours}h${minutes}m` : `${minutes}m`;
    }

    // 初始化控制面板值
    initializeControlPanelValues() {
        // 设置初始速度
        this.updateSpeed(this.currentState.currentSpeed);
        
        // 设置连续阅读
        if (this.continuousReadingToggle) {
            this.continuousReadingToggle.checked = this.currentState.continuousReading;
        }
        
        // 设置章节数量
        this.updateChapterCount(this.currentState.continuousChapters);
        
        // 设置播放时长
        const percentage = this.currentState.readingDuration / this.MAX_DURATION_SECONDS;
        this.updateDurationUI(percentage);
    }

    // 启动状态同步
    startStateSynchronization() {
        // 定期请求状态更新
        setInterval(() => {
            this.requestStateUpdate();
        }, 5000);
    }

    // 请求状态更新
    requestStateUpdate() {
        this.dependencies.chrome.runtime.sendMessage({ action: 'getState' }, (response) => {
            if (!this.dependencies.chrome.runtime.lastError && response) {
                this.updateState(response);
            }
        });
    }

    // 更新状态
    updateState(newState) {
        const oldState = { ...this.currentState };
        this.currentState = { ...this.currentState, ...newState };
        
        // 更新UI
        this.updatePlaybackUI();
        
        // 处理状态变化
        if (oldState.readingState !== this.currentState.readingState) {
            this.handleStateChange(oldState.readingState, this.currentState.readingState);
        }
    }

    // 更新播放UI
    updatePlaybackUI() {
        // 更新播放/暂停按钮
        if (this.playPauseButton) {
            this.playPauseButton.classList.toggle('playing', this.currentState.readingState === 'reading');
        }
        
        // 更新状态指示器
        if (this.statusIndicatorEl) {
            this.statusIndicatorEl.textContent = this.translateState(this.currentState.readingState);
            this.statusIndicatorEl.className = `status-indicator ${this.currentState.readingState}`;
        }
        
        // 更新速度显示
        if (this.currentState.currentSpeed !== undefined) {
            this.updateSpeed(this.currentState.currentSpeed);
        }
    }

    // 处理状态变化
    handleStateChange(oldState, newState) {
        console.log(`状态变化: ${oldState} -> ${newState}`);
        
        switch (newState) {
            case 'reading':
                this.startPlaybackTimer();
                break;
            case 'paused':
                this.pausePlaybackTimer();
                break;
            case 'stopped':
            case 'idle':
                this.resetPlaybackTimer();
                break;
        }
    }

    // 翻译状态
    translateState(state) {
        const stateMap = {
            'idle': '空闲',
            'reading': '播放中',
            'paused': '已暂停',
            'stopped': '已停止',
            'loading': '加载中',
            'navigating': '跳转中',
            'error': '错误'
        };
        return stateMap[state] || state;
    }

    // 更新停止后状态
    updateStateAfterStop() {
        this.currentState.readingState = 'stopped';
        this.updatePlaybackUI();
        this.resetPlaybackTimer();
    }

    // 显示消息
    showMessage(message, isError = false) {
        // 简单的消息显示实现
        console.log(isError ? '❌' : 'ℹ️', message);
    }

    // 公共API
    getCurrentState() {
        return { ...this.currentState };
    }

    setCurrentState(newState) {
        this.updateState(newState);
    }

    getPlaybackTime() {
        return {
            elapsed: this.playbackElapsedTime,
            total: this.totalPlaybackTime,
            remaining: this.remainingTime
        };
    }
}
