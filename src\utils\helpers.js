// General Helper Functions for 神灯 AI 朗读增强助手

/**
 * Debounces a function, ensuring it's only called after a certain delay
 * since the last time it was invoked.
 * @param {function} func The function to debounce.
 * @param {number} wait The debounce delay in milliseconds.
 * @param {boolean} immediate If true, call the function immediately and then wait for the delay.
 * @returns {function} The debounced function.
 */
export function debounce(func, wait, immediate = false) {
    let timeout;
    return function(...args) {
        const context = this;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

/**
 * Throttles a function, ensuring it's called at most once within a specified time limit.
 * @param {function} func The function to throttle.
 * @param {number} limit The throttle time limit in milliseconds.
 * @returns {function} The throttled function.
 */
export function throttle(func, limit) {
    let inThrottle;
    let lastResult;
    return function(...args) {
        const context = this;
        if (!inThrottle) {
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
            lastResult = func.apply(context, args);
        }
        return lastResult;
    };
}

/**
 * Simple text chunking function.
 * Splits text into chunks based on sentences or max length.
 * TODO: Improve this logic for better natural breaks.
 * @param {string} text The text to chunk.
 * @param {number} maxLength Maximum length of a chunk (approximate).
 * @returns {string[]} An array of text chunks.
 */
export function chunkText(text, maxLength = 200) {
    if (!text) return [];
    
    // Simple sentence splitting (can be improved with regex for punctuation)
    const sentences = text.match(/[^.!?]+[.!?]*/g) || [text]; 
    const chunks = [];
    let currentChunk = '';

    for (const sentence of sentences) {
        if (currentChunk.length + sentence.length <= maxLength) {
            currentChunk += sentence + ' '; // Add space between sentences
        } else {
            // If adding the sentence exceeds max length, push the current chunk
            if (currentChunk.trim()) {
                chunks.push(currentChunk.trim());
            }
            // Start a new chunk with the current sentence
            // If the sentence itself is longer than maxLength, split it hard (less ideal)
            if (sentence.length > maxLength) {
                for (let i = 0; i < sentence.length; i += maxLength) {
                     chunks.push(sentence.substring(i, i + maxLength).trim());
                 }
                 currentChunk = ''; // Reset chunk after hard split
            } else {
                 currentChunk = sentence + ' ';
            }
           
        }
    }

    // Add the last remaining chunk
    if (currentChunk.trim()) {
        chunks.push(currentChunk.trim());
    }

    return chunks.filter(chunk => chunk); // Ensure no empty chunks
}


/**
 * Escapes HTML special characters to prevent XSS.
 * @param {string} unsafe String containing potentially unsafe HTML.
 * @returns {string} Escaped string.
 */
export function escapeHTML(unsafe) {
    if (!unsafe) return '';
    return unsafe
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
 }

console.log("Helper utilities initialized.");
