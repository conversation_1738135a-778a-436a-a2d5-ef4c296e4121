// Options Page Logic for 神灯 AI 朗读增强助手

console.log("Options script loaded.");

// --- DOM Elements ---
const defaultSpeedSlider = document.getElementById('default-speed');
const defaultSpeedValueEl = document.getElementById('default-speed-value');
const defaultVoiceSelect = document.getElementById('default-voice');
const defaultContinuousReadingCheckbox = document.getElementById('default-continuous-reading');
const ttsEngineSelect = document.getElementById('tts-engine-select');
const apiKeyConfigSection = document.querySelector('.api-key-config');
const apiKeyInput = document.getElementById('api-key-input');
const apiStatusEl = document.querySelector('.api-status');
const saveButton = document.getElementById('save-button');
const saveStatusEl = document.getElementById('save-status');
const versionEl = document.getElementById('version');

// --- Functions ---

/**
 * Loads settings from chrome.storage.
 */
async function loadSettings() {
    console.log("Loading settings...");
    saveStatusEl.textContent = '加载中...';
    try {
        const items = await chrome.storage.local.get([
            'currentSpeed', // Using currentSpeed as the default speed basis
            'currentVoice', // Using currentVoice as the default voice basis
            'continuousReading', // Using this as the default basis
            'ttsEngine',      // Planned setting
            'apiKey'          // Planned setting
        ]);
        console.log("Settings loaded:", items);

        // Apply loaded settings to the UI
        defaultSpeedSlider.value = items.currentSpeed || 1.0;
        defaultSpeedValueEl.textContent = `${parseFloat(defaultSpeedSlider.value).toFixed(1)}x`;
        
        defaultContinuousReadingCheckbox.checked = items.continuousReading !== undefined ? items.continuousReading : true;
        
        // Populate voices before setting the value
        await populateVoiceList(items.currentVoice); 

        // Placeholder for future settings
        // ttsEngineSelect.value = items.ttsEngine || 'browser';
        // apiKeyInput.value = items.apiKey || '';
        // updateApiKeyStatus(items.apiKey);
        // toggleApiKeySection();

        saveStatusEl.textContent = '设置已加载';
    } catch (error) {
        console.error("Error loading settings:", error);
        saveStatusEl.textContent = `加载失败: ${error.message}`;
        saveStatusEl.style.color = 'red';
    }
}

/**
 * Saves settings to chrome.storage.
 */
async function saveSettings() {
    console.log("Saving settings...");
    saveButton.disabled = true;
    saveStatusEl.textContent = '保存中...';
    saveStatusEl.style.color = 'inherit';

    const settingsToSave = {
        currentSpeed: parseFloat(defaultSpeedSlider.value),
        currentVoice: defaultVoiceSelect.value,
        continuousReading: defaultContinuousReadingCheckbox.checked,
        // ttsEngine: ttsEngineSelect.value, // Planned
        // apiKey: apiKeyInput.value // Planned - consider encrypting this!
    };

    try {
        await chrome.storage.local.set(settingsToSave);
        console.log("Settings saved:", settingsToSave);
        saveStatusEl.textContent = '设置已保存!';
        saveStatusEl.style.color = 'green';
        setTimeout(() => { saveStatusEl.textContent = ''; }, 3000); // Clear status after 3s
    } catch (error) {
        console.error("Error saving settings:", error);
        saveStatusEl.textContent = `保存失败: ${error.message}`;
        saveStatusEl.style.color = 'red';
    } finally {
        saveButton.disabled = false;
    }
}

/**
 * Populates the voice selection dropdown.
 */
async function populateVoiceList(currentVoiceValue) {
    defaultVoiceSelect.innerHTML = '<option value="">加载中...</option>';
    try {
        const voices = await new Promise(resolve => chrome.tts.getVoices(resolve));
        defaultVoiceSelect.innerHTML = ''; // Clear loading message

        if (!voices || voices.length === 0) {
            defaultVoiceSelect.innerHTML = '<option value="" disabled>无可用语音</option>';
            return;
        }

        const defaultOption = document.createElement('option');
        defaultOption.value = ""; // Represent browser default/auto
        defaultOption.textContent = "默认/自动";
        defaultVoiceSelect.appendChild(defaultOption);

        voices.forEach(voice => {
            const option = document.createElement('option');
            option.value = voice.voiceName;
            option.textContent = `${voice.voiceName} (${voice.lang})` + (voice.gender ? ` - ${voice.gender}` : '');
            defaultVoiceSelect.appendChild(option);
        });
        
        // Set the saved value after populating
        defaultVoiceSelect.value = currentVoiceValue || ""; 
        if (defaultVoiceSelect.selectedIndex === -1) defaultVoiceSelect.selectedIndex = 0; // Fallback

    } catch (error) {
        console.error("Error getting TTS voices:", error);
        defaultVoiceSelect.innerHTML = '<option value="" disabled>获取语音失败</option>';
    }
}

// --- Planned Features --- 
/*
function toggleApiKeySection() {
    const selectedEngine = ttsEngineSelect.value;
    if (selectedEngine === 'azure' || selectedEngine === 'google') { // Engines requiring API keys
        apiKeyConfigSection.style.display = 'block';
    } else {
        apiKeyConfigSection.style.display = 'none';
    }
}

function updateApiKeyStatus(apiKey) {
    if (apiKey && apiKey.length > 5) { // Basic check
        apiStatusEl.textContent = '已配置';
        apiStatusEl.style.color = 'green';
    } else {
        apiStatusEl.textContent = '未配置';
        apiStatusEl.style.color = 'orange';
    }
}
*/

// --- Event Listeners ---
defaultSpeedSlider.addEventListener('input', () => {
    defaultSpeedValueEl.textContent = `${parseFloat(defaultSpeedSlider.value).toFixed(1)}x`;
});

voiceSelect.addEventListener('change', () => {
    saveSettings(); // 保存语音设置
});

saveButton.addEventListener('click', saveSettings);

// ttsEngineSelect.addEventListener('change', toggleApiKeySection); // Planned
// apiKeyInput.addEventListener('input', () => updateApiKeyStatus(apiKeyInput.value)); // Planned

// --- Initialization ---
function initializeOptionsPage() {
    // Display version from manifest
    const manifest = chrome.runtime.getManifest();
    versionEl.textContent = manifest.version || 'N/A';

    loadSettings();
}

// Run initialization
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeOptionsPage);
} else {
    initializeOptionsPage();
}
