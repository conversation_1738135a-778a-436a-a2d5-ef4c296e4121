<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音设置 - 神灯AI·灵阅</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="voice-settings-container">
        <!-- 顶部标题区域 -->
        <div class="voice-settings-header">
            <h2>语音设置</h2>
            <button id="close-voice-settings-btn" class="close-btn" title="关闭">×</button>
        </div>

        <!-- 搜索与筛选区域 -->
        <div class="card filter-card">
            <div class="search-box">
                <input type="text" id="voice-search-input" placeholder="搜索语音..." class="voice-search-input">
            </div>
            
            <div class="filter-tags language-tags">
                <button class="filter-tag active" data-filter="all">全部</button>
                <button class="filter-tag" data-filter="zh" data-type="language">中文</button>
                <button class="filter-tag" data-filter="en" data-type="language">英文</button>
                <button class="filter-tag" data-filter="ja" data-type="language">日文</button>
                <button class="filter-tag" data-filter="fr" data-type="language">法文</button>
                <button class="filter-tag" data-filter="de" data-type="language">德文</button>
                <button class="filter-tag" data-filter="ru" data-type="language">俄文</button>
                <button class="filter-tag" data-filter="ko" data-type="language">韩文</button>
            </div>
            
            <div class="filter-tags dialect-tags" id="dialect-tags-container">
                <!-- 方言标签会通过JS动态填充，平铺显示 -->
            </div>
        </div>

        <!-- 语音列表区域 -->
        <div class="card voices-list-card">
            <h3>所有语音</h3>
            
            <div id="voices-list" class="voices-list">
                <!-- 语音列表会通过JavaScript动态填充 -->
                <div id="loading-message" class="loading-message">加载中...</div>
            </div>
        </div>

        <!-- AI语音区域（底部区域） -->
        <div class="card ai-tts-card">
            <div class="ai-tts-header">
                <h3>AI语音设置</h3>
                <span class="badge coming-soon">即将推出</span>
            </div>
            <div class="ai-tts-content">
                <p class="ai-tts-message">神灯AI语音功能即将推出，敬请期待！</p>
            </div>
        </div>

        <!-- 保存设置按钮 -->
        <div class="save-button-container">
            <button id="save-settings-btn" class="save-settings-btn">保存设置</button>
        </div>
    </div>

    <!-- 语音项模板 -->
    <template id="voice-item-template">
        <div class="voice-item">
            <div class="voice-item-content">
                <div class="voice-item-header">
                    <span class="priority-indicator" style="display: none;">⭐</span>
                    <div class="voice-name"></div>
                </div>
                
                <div class="voice-tags"></div>
            </div>
            
            <div class="voice-actions">
                <button class="voice-action-btn preview-btn" title="试听">🔊</button>
                <button class="voice-action-btn favorite-btn" title="收藏">☆</button>
                <button class="voice-action-btn default-btn" title="设为默认">○</button>
            </div>
        </div>
    </template>

    <!-- 空状态模板 -->
    <template id="empty-state-template">
        <div class="empty-state">
            <div class="empty-state-title"></div>
            <div class="empty-state-message"></div>
        </div>
    </template>

    <script src="index.js" type="module"></script>
</body>
</html> 