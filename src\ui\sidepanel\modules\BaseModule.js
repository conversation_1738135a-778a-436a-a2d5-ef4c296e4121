// 基础模块类 - 所有UI模块的基类
import { errorHandler } from '../../../utils/errorHandler.js';
import { performanceMonitor } from '../../../utils/performanceMonitor.js';

export class BaseModule {
    constructor(name, dependencies = {}) {
        this.name = name;
        this.dependencies = dependencies;
        this.isEnabled = true;
        this.isInitialized = false;
        this.eventListeners = new Map();
        
        // 性能监控
        this.performanceMonitor = performanceMonitor;
        this.errorHandler = errorHandler;
        
        console.log(`📦 模块 ${this.name} 已创建`);
    }

    // 初始化模块
    async init() {
        if (this.isInitialized) {
            console.warn(`⚠️ 模块 ${this.name} 已经初始化过了`);
            return;
        }

        try {
            console.log(`🚀 初始化模块 ${this.name}`);
            
            // 性能监控开始
            this.performanceMonitor.startTimer(`${this.name}_init`);
            
            // 调用子类的初始化方法
            await this.onInit();
            
            this.isInitialized = true;
            
            // 性能监控结束
            const timing = this.performanceMonitor.endTimer(`${this.name}_init`);
            console.log(`✅ 模块 ${this.name} 初始化完成，耗时: ${timing?.duration?.toFixed(2)}ms`);
            
        } catch (error) {
            this.errorHandler.handleError(error, `${this.name}_init`, {
                module: this.name,
                action: 'initialization'
            });
            throw error;
        }
    }

    // 子类需要重写的初始化方法
    async onInit() {
        // 子类实现具体的初始化逻辑
    }

    // 销毁模块
    async destroy() {
        if (!this.isInitialized) {
            return;
        }

        try {
            console.log(`🗑️ 销毁模块 ${this.name}`);
            
            // 清理事件监听器
            this.clearAllEventListeners();
            
            // 调用子类的销毁方法
            await this.onDestroy();
            
            this.isInitialized = false;
            console.log(`✅ 模块 ${this.name} 已销毁`);
            
        } catch (error) {
            this.errorHandler.handleError(error, `${this.name}_destroy`, {
                module: this.name,
                action: 'destruction'
            });
        }
    }

    // 子类需要重写的销毁方法
    async onDestroy() {
        // 子类实现具体的清理逻辑
    }

    // 启用模块
    enable() {
        this.isEnabled = true;
        console.log(`✅ 模块 ${this.name} 已启用`);
    }

    // 禁用模块
    disable() {
        this.isEnabled = false;
        console.log(`❌ 模块 ${this.name} 已禁用`);
    }

    // 检查模块是否可用
    isAvailable() {
        return this.isEnabled && this.isInitialized;
    }

    // 安全地添加事件监听器
    addEventListener(element, event, handler, name = null) {
        if (!element || typeof handler !== 'function') {
            console.error(`❌ 模块 ${this.name}: 无效的事件监听器参数`);
            return;
        }

        try {
            const listenerName = name || `${event}_${Date.now()}`;
            const wrappedHandler = this.wrapEventHandler(handler, listenerName);
            
            element.addEventListener(event, wrappedHandler);
            
            // 记录监听器以便后续清理
            this.eventListeners.set(listenerName, {
                element,
                event,
                handler: wrappedHandler
            });
            
            console.log(`🎧 模块 ${this.name}: 添加事件监听器 ${listenerName}`);
            
        } catch (error) {
            this.errorHandler.handleError(error, `${this.name}_addEventListener`, {
                module: this.name,
                event,
                listenerName: name
            });
        }
    }

    // 包装事件处理器，添加错误处理和性能监控
    wrapEventHandler(handler, name) {
        return (event) => {
            if (!this.isAvailable()) {
                console.log(`⏸️ 模块 ${this.name} 不可用，跳过事件处理: ${name}`);
                return;
            }

            try {
                // 性能监控
                this.performanceMonitor.startTimer(`${this.name}_${name}`);
                
                // 执行原始处理器
                const result = handler.call(this, event);
                
                // 如果返回Promise，等待完成
                if (result && typeof result.then === 'function') {
                    result
                        .then(() => {
                            this.performanceMonitor.endTimer(`${this.name}_${name}`);
                        })
                        .catch((error) => {
                            this.performanceMonitor.endTimer(`${this.name}_${name}`);
                            this.errorHandler.handleError(error, `${this.name}_${name}`, {
                                module: this.name,
                                event: event.type
                            });
                        });
                } else {
                    this.performanceMonitor.endTimer(`${this.name}_${name}`);
                }
                
                return result;
                
            } catch (error) {
                this.performanceMonitor.endTimer(`${this.name}_${name}`);
                this.errorHandler.handleError(error, `${this.name}_${name}`, {
                    module: this.name,
                    event: event.type
                });
            }
        };
    }

    // 移除事件监听器
    removeEventListener(name) {
        const listener = this.eventListeners.get(name);
        if (listener) {
            try {
                listener.element.removeEventListener(listener.event, listener.handler);
                this.eventListeners.delete(name);
                console.log(`🔇 模块 ${this.name}: 移除事件监听器 ${name}`);
            } catch (error) {
                this.errorHandler.handleError(error, `${this.name}_removeEventListener`, {
                    module: this.name,
                    listenerName: name
                });
            }
        }
    }

    // 清理所有事件监听器
    clearAllEventListeners() {
        console.log(`🧹 模块 ${this.name}: 清理所有事件监听器`);
        
        for (const [name, listener] of this.eventListeners) {
            try {
                listener.element.removeEventListener(listener.event, listener.handler);
            } catch (error) {
                console.error(`清理事件监听器 ${name} 时出错:`, error);
            }
        }
        
        this.eventListeners.clear();
    }

    // 安全地获取DOM元素
    getElement(selector, required = true) {
        try {
            const element = document.querySelector(selector);
            
            if (!element && required) {
                throw new Error(`必需的DOM元素未找到: ${selector}`);
            }
            
            return element;
            
        } catch (error) {
            this.errorHandler.handleError(error, `${this.name}_getElement`, {
                module: this.name,
                selector,
                required
            });
            
            if (required) {
                throw error;
            }
            
            return null;
        }
    }

    // 安全地获取多个DOM元素
    getElements(selector) {
        try {
            return document.querySelectorAll(selector);
        } catch (error) {
            this.errorHandler.handleError(error, `${this.name}_getElements`, {
                module: this.name,
                selector
            });
            return [];
        }
    }

    // 发送消息到后台脚本
    async sendMessage(message) {
        try {
            return new Promise((resolve, reject) => {
                chrome.runtime.sendMessage(message, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
        } catch (error) {
            this.errorHandler.handleError(error, `${this.name}_sendMessage`, {
                module: this.name,
                message
            });
            throw error;
        }
    }

    // 获取依赖
    getDependency(name) {
        const dependency = this.dependencies[name];
        if (!dependency) {
            console.warn(`⚠️ 模块 ${this.name}: 未找到依赖 ${name}`);
        }
        return dependency;
    }

    // 检查依赖是否存在
    hasDependency(name) {
        return name in this.dependencies && this.dependencies[name] != null;
    }

    // 模块状态信息
    getStatus() {
        return {
            name: this.name,
            isEnabled: this.isEnabled,
            isInitialized: this.isInitialized,
            eventListeners: this.eventListeners.size,
            dependencies: Object.keys(this.dependencies)
        };
    }

    // 日志方法
    log(message, level = 'info') {
        const prefix = `[${this.name}]`;
        switch (level) {
            case 'error':
                console.error(prefix, message);
                break;
            case 'warn':
                console.warn(prefix, message);
                break;
            case 'debug':
                console.debug(prefix, message);
                break;
            default:
                console.log(prefix, message);
        }
    }
}

export default BaseModule;
