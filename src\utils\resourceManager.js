// 资源管理器 - 统一管理定时器、事件监听器等资源
class ResourceManager {
    constructor() {
        this.timers = new Map();
        this.listeners = new Map();
        this.intervals = new Map();
        this.timeouts = new Map();
    }

    // 创建并管理定时器
    createInterval(name, callback, delay) {
        this.clearInterval(name); // 清除同名定时器
        const id = setInterval(callback, delay);
        this.intervals.set(name, id);
        return id;
    }

    createTimeout(name, callback, delay) {
        this.clearTimeout(name);
        const id = setTimeout(() => {
            callback();
            this.timeouts.delete(name); // 自动清理
        }, delay);
        this.timeouts.set(name, id);
        return id;
    }

    // 清理定时器
    clearInterval(name) {
        if (this.intervals.has(name)) {
            clearInterval(this.intervals.get(name));
            this.intervals.delete(name);
        }
    }

    clearTimeout(name) {
        if (this.timeouts.has(name)) {
            clearTimeout(this.timeouts.get(name));
            this.timeouts.delete(name);
        }
    }

    // 管理事件监听器
    addEventListener(element, event, handler, name) {
        this.removeEventListener(name);
        element.addEventListener(event, handler);
        this.listeners.set(name, { element, event, handler });
    }

    removeEventListener(name) {
        if (this.listeners.has(name)) {
            const { element, event, handler } = this.listeners.get(name);
            element.removeEventListener(event, handler);
            this.listeners.delete(name);
        }
    }

    // 清理所有资源
    cleanup() {
        // 清理所有定时器
        this.intervals.forEach((id) => clearInterval(id));
        this.timeouts.forEach((id) => clearTimeout(id));
        
        // 清理所有事件监听器
        this.listeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });

        // 清空所有映射
        this.intervals.clear();
        this.timeouts.clear();
        this.listeners.clear();
    }

    // 获取资源使用情况（调试用）
    getResourceStats() {
        return {
            intervals: this.intervals.size,
            timeouts: this.timeouts.size,
            listeners: this.listeners.size
        };
    }
}

// 创建全局资源管理器实例
export const resourceManager = new ResourceManager();

// 页面卸载时自动清理
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
        resourceManager.cleanup();
    });
}

console.log("资源管理器已初始化");
