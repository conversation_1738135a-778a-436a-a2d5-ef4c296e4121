// PDF.js worker script
// This file is loaded by PDF.js to handle PDF processing in a separate thread

// Initialize PDF.js worker
importScripts(chrome.runtime.getURL('src/lib/pdf.js'));

// Set up worker communication
self.onmessage = function(e) {
  const data = e.data;
  if (data.type === 'initialize') {
    // Initialize worker
    pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('src/lib/pdf.worker.min.js');
    self.postMessage({ type: 'initialized' });
  } else if (data.type === 'process') {
    // Process PDF data
    // Implementation will depend on specific PDF processing needs
    self.postMessage({ type: 'processed', result: data });
  }
};
