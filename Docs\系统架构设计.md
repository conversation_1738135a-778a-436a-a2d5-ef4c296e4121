一、 系统架构 (System Architecture) - 基于 Manifest V3
我们将采用现代浏览器扩展的标准架构 (Manifest V3)，主要包含以下几个部分：

Service Worker (Background):
职责: 作为扩展的事件处理中心，处理浏览器事件 (如安装、标签页更新)、管理扩展的长期状态、与外部 API (TTS, LLM) 通信、协调其他部分。在 Manifest V3 中，它取代了持久的背景页，按需运行。
技术: JavaScript 或 TypeScript。
Content Scripts:
职责: 注入到用户浏览的网页中，直接与页面的 DOM 交互。负责抓取小说/文章内容、识别“下一页/章”按钮、高亮正在朗读的文本、将用户操作（如选中文本）通知给 Service Worker。
技术: JavaScript 或 TypeScript。
UI (用户界面):
Side Panel (首选) / Popup:
职责: 提供主要的交互界面，用于显示阅读控制按钮 (播放/暂停/语速/音量)、语音选择、阅读进度、设置入口，以及展示 AI 功能的结果 (WIKI 查询、摘要、图谱等)。Side Panel API (较新) 允许更持久和宽敞的 UI 空间，比 Popup 更适合复杂交互。
技术: HTML, CSS, JavaScript/TypeScript。可以考虑使用轻量级 UI 框架 (如 React, Vue, Svelte, Preact) 来提高开发效率和组件化。
Options Page:
职责: 提供更详细的配置选项，如 API Key 设置、本地模型端点配置、默认语音偏好、功能开关等。
技术: HTML, CSS, JavaScript/TypeScript。
Storage:
职责: 持久化存储用户设置、阅读进度、收藏的语音、API Keys (安全存储) 等。
技术: 使用 chrome.storage.local (本地存储) 或 chrome.storage.sync (跨设备同步，有容量限制)。敏感信息（如 API Key）需要额外加密处理或通过更安全的方式管理。
通讯机制:
各部分之间通过 chrome.runtime.sendMessage 和 chrome.tabs.sendMessage 以及相关的监听器 (onMessage) 进行通信。
核心逻辑/模块 (Core Logic / Modules):
职责: 封装可重用的核心功能，当前已实现：
PageParser: 网页内容解析与下一页/章识别逻辑。
TTSManager: 管理浏览器内置 TTS 引擎，处理语音播放控制。
StateManager: 管理应用的核心状态（阅读状态、设置等）。

计划实现：
LLMManager: 抽象与 LLM API 或本地模型的交互，处理摘要、WIKI 构建等任务。
WikiBuilder: 负责从文本构建和查询 WIKI 数据。
RoleRecognizer: 负责识别对话角色。
技术: JavaScript/TypeScript。这些模块会被 Service Worker、UI 或 Content Scripts 按需调用。
二、 目录结构 (Directory Structure)
CopyInsert
神灯AI朗读增强助手/
├── docs/                  # 项目文档 (现有)
│   ├── 功能说明.md
│   ├── 原始开发需求说明.md
│   └── 阅读插件设计方案.md
├── dist/                  # 打包后的扩展文件 (构建生成, Git忽略)
├── scripts/               # 构建、部署等辅助脚本
│   └── build.js           # (示例)
├── src/                   # 源代码
│   ├── background/        # Service Worker 相关代码
│   │   └── index.js       # Service Worker 入口
│   ├── content/           # Content Scripts 相关代码
│   │   ├── parser.js      # 页面内容解析逻辑
│   │   └── injector.js    # 注入到页面的主脚本
│   ├── core/              # 核心功能模块 (或称 lib/modules)
│   │   ├── tts-manager.js
│   │   ├── llm-manager.js
│   │   ├── state-manager.js
│   │   ├── wiki-builder.js
│   │   └── role-recognizer.js
│   ├── ui/                # 用户界面相关代码
│   │   ├── components/    # (若使用框架) 可复用UI组件
│   │   │   └── PlayerControls.jsx # (示例)
│   │   ├── sidepanel/     # 侧边栏 UI
│   │   │   ├── index.html
│   │   │   ├── index.js   # (或 index.jsx 若用React)
│   │   │   └── styles.css
│   │   ├── options/       # 选项页 UI
│   │   │   ├── index.html
│   │   │   ├── index.js
│   │   │   └── styles.css
│   │   └── popup/         # (备选) 弹窗 UI
│   │       ├── index.html
│   │       ├── index.js
│   │       └── styles.css
│   ├── utils/             # 通用工具函数
│   │   ├── storage.js     # 封装 chrome.storage
│   │   └── helpers.js
│   └── assets/            # 静态资源
│       ├── icons/         # 扩展图标 (16, 32, 48, 128)
│       │   ├── icon16.png
│       │   └── ...
│       └── fonts/         # (若需要) 自定义字体
├── manifest.json          # 扩展配置文件 (核心)
├── package.json           # 项目依赖与脚本 (若使用npm)
├── README.md              # 项目说明
└── .gitignore             # Git 忽略配置
三、 文件命名规则 (File Naming Conventions)
目录 (Folders): 使用 kebab-case (短横线连接的小写字母)，例如 background, content-scripts, ui-components。
JavaScript/TypeScript 文件:
普通脚本、模块、工具函数: 使用 camelCase.js (驼峰命名)，例如 ttsManager.js, pageParser.js, utilsHelper.js。
UI 组件文件 (若使用 React/Vue 等): 使用 PascalCase.jsx 或 PascalCase.vue (大驼峰)，例如 ReaderControls.jsx, SettingsForm.vue。
入口文件: 可使用 index.js。
CSS/SCSS 文件: 使用 kebab-case.css 或 styles.css，例如 sidepanel-styles.css。
HTML 文件: 使用 kebab-case.html 或 index.html，例如 options-page.html。
类 (Classes): 使用 PascalCase。
函数 (Functions) 和变量 (Variables): 使用 camelCase。
常量 (Constants): 使用 UPPER_SNAKE_CASE (全大写下划线连接)，例如 DEFAULT_SPEED, API_ENDPOINT。
建议:

技术选型: 建议使用 TypeScript 以获得更好的类型检查和代码提示，尤其对于中大型项目。考虑使用 Webpack 或 Vite 等现代构建工具来处理模块打包、代码转换 (TS/JSX) 和资源管理。
一致性: 最重要的是在整个项目中保持命名和结构风格的一致性。
这套架构和规范为您提供了一个清晰的起点。您可以根据项目的具体进展和技术选型进行微调。接下来我们可以开始创建这些基础目录和配置文件了。