// Storage Utility Functions for 神灯 AI 朗读增强助手

/**
 * Gets one or more items from chrome.storage.local.
 * @param {string | string[] | object | null} keys A single key, array of keys, or object like {key: defaultValue}.
 * @returns {Promise<object>} A promise that resolves with an object containing the requested key-value pairs.
 */
export function getStorage(keys) {
    return new Promise((resolve, reject) => {
        chrome.storage.local.get(keys, (items) => {
            if (chrome.runtime.lastError) {
                console.error("Error getting from storage:", chrome.runtime.lastError);
                return reject(chrome.runtime.lastError);
            }
            resolve(items);
        });
    });
}

/**
 * Sets one or more items in chrome.storage.local.
 * @param {object} items An object containing key-value pairs to store.
 * @returns {Promise<void>} A promise that resolves when the items have been successfully stored.
 */
export function setStorage(items) {
    return new Promise((resolve, reject) => {
        chrome.storage.local.set(items, () => {
            if (chrome.runtime.lastError) {
                console.error("Error setting storage:", chrome.runtime.lastError);
                return reject(chrome.runtime.lastError);
            }
            resolve();
        });
    });
}

/**
 * Removes one or more items from chrome.storage.local.
 * @param {string | string[]} keys A single key or an array of keys to remove.
 * @returns {Promise<void>} A promise that resolves when the items have been successfully removed.
 */
export function removeStorage(keys) {
    return new Promise((resolve, reject) => {
        chrome.storage.local.remove(keys, () => {
            if (chrome.runtime.lastError) {
                console.error("Error removing from storage:", chrome.runtime.lastError);
                return reject(chrome.runtime.lastError);
            }
            resolve();
        });
    });
}

/**
 * Clears all items from chrome.storage.local.
 * Use with caution!
 * @returns {Promise<void>} A promise that resolves when the storage has been cleared.
 */
export function clearStorage() {
    return new Promise((resolve, reject) => {
        chrome.storage.local.clear(() => {
            if (chrome.runtime.lastError) {
                console.error("Error clearing storage:", chrome.runtime.lastError);
                return reject(chrome.runtime.lastError);
            }
            resolve();
        });
    });
}

// Optional: Listener for storage changes (useful for debugging or cross-context updates)
// chrome.storage.local.onChanged.addListener((changes, areaName) => {
//     if (areaName === 'local') {
//         console.log('Storage changed:', changes);
//         // Can trigger state updates here if needed
//     }
// });

console.log("Storage utility initialized.");
