// 全局阅读状态管理

// 阅读状态对象
const ReadingState = {
  // 基础状态
  status: 'idle', // idle, reading, paused, navigating
  currentSpeed: 1.1,
  currentVoice: null,
  
  // 阅读源信息
  sourceInfo: {
    url: null,
    title: null,
    tabId: null,
    windowId: null
  },
  
  // 阅读进度
  progress: {
    position: 0,
    totalLength: 0,
    currentChapter: 0,
    totalChapters: 0
  },
  
  // 设置
  settings: {
    continuousReading: true,
    readingDuration: 1800, // 30分钟
    continuousChapters: 30
  }
};

// 状态更新事件处理器
const stateChangeHandlers = new Set();

// 注册状态变更监听器
function onStateChange(handler) {
  stateChangeHandlers.add(handler);
  return () => stateChangeHandlers.delete(handler);
}

// 更新状态并触发事件
function updateState(updates) {
  const oldState = { ...ReadingState };
  
  // 深度合并更新
  Object.entries(updates).forEach(([key, value]) => {
    if (typeof value === 'object' && value !== null) {
      ReadingState[key] = { ...ReadingState[key], ...value };
    } else {
      ReadingState[key] = value;
    }
  });
  
  // 触发状态变更事件
  stateChangeHandlers.forEach(handler => handler(ReadingState, oldState));
}

// 页面切换处理
async function handlePageChange(newTabId, newUrl, newTitle) {
  const oldSourceInfo = { ...ReadingState.sourceInfo };
  
  // 如果正在阅读，且切换到了新页面
  if (ReadingState.status === 'reading' && newTabId !== oldSourceInfo.tabId) {
    // 更新状态为navigating
    updateState({
      status: 'navigating',
      sourceInfo: {
        url: newUrl,
        title: newTitle,
        tabId: newTabId
      }
    });
    
    // 发送状态更新到UI
    await sendStateUpdateToUI();
  }
}

// 恢复到原阅读页面
async function returnToSource() {
  const { tabId, windowId } = ReadingState.sourceInfo;
  if (tabId && windowId) {
    try {
      await chrome.windows.update(windowId, { focused: true });
      await chrome.tabs.update(tabId, { active: true });
    } catch (error) {
      console.error('返回原页面失败:', error);
    }
  }
}

// 切换到新页面阅读
async function switchToNewPage(tabId, url, title) {
  updateState({
    status: 'reading',
    sourceInfo: {
      url,
      title,
      tabId,
      windowId: await chrome.windows.getCurrent().id
    },
    progress: {
      position: 0,
      totalLength: 0,
      currentChapter: 0,
      totalChapters: 0
    }
  });
  
  await sendStateUpdateToUI();
}

// 发送状态更新到UI
async function sendStateUpdateToUI() {
  try {
    await chrome.runtime.sendMessage({
      action: 'stateUpdate',
      state: ReadingState
    });
  } catch (error) {
    console.warn('发送状态更新失败:', error);
  }
}

// 导出接口
export {
  ReadingState,
  onStateChange,
  updateState,
  handlePageChange,
  returnToSource,
  switchToNewPage
}; 