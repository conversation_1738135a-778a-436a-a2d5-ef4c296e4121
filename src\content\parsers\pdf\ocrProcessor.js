const { createWorker } = require('tesseract.js');

class OcrProcessor {
    constructor() {
        this.worker = null;
        this.isInitialized = false;
        this.initializing = false;
    }

    async initialize(lang = 'chi_sim') {
        if (this.isInitialized) return;
        if (this.initializing) return;
        
        this.initializing = true;
        try {
            this.worker = await createWorker({
                logger: m => console.log(m),
                workerPath: chrome.runtime.getURL('src/lib/tesseract/worker.min.js'),
                corePath: chrome.runtime.getURL('src/lib/tesseract/tesseract-core.wasm.js'),
                langPath: chrome.runtime.getURL('src/lib/tesseract/lang/')
            });
            
            await this.worker.load();
            await this.worker.loadLanguage(lang);
            await this.worker.initialize(lang);
            this.isInitialized = true;
        } catch (error) {
            console.error('OCR初始化失败:', error);
            this.worker = null;
            throw error;
        } finally {
            this.initializing = false;
        }
    }

    async recognize(canvas) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        if (!this.worker) {
            throw new Error('OCR处理器未初始化');
        }

        try {
            const { data: { text } } = await this.worker.recognize(canvas);
            return text;
        } catch (error) {
            console.error('OCR识别失败:', error);
            this.worker.terminate();
            this.worker = null;
            this.isInitialized = false;
            throw error;
        }
    }

    async terminate() {
        if (this.worker) {
            await this.worker.terminate();
            this.worker = null;
            this.isInitialized = false;
        }
    }

    get isReady() {
        return this.isInitialized;
    }
}

module.exports = { OcrProcessor };
