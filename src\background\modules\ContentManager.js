// 内容管理模块 - 处理网页内容解析、缓存、预加载等功能
import { BaseBackgroundModule } from './BaseBackgroundModule.js';

export class ContentManager extends BaseBackgroundModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // 内容缓存
        this.tabContentCache = new Map();
        this.preloadedChapters = [];
        this.maxCacheSize = 50; // 最大缓存页面数
        
        // 当前阅读状态
        this.currentReadingTabId = null;
        this.currentChapterCount = 0;
        this.nextChapterInfo = null;
        this.currentArticle = null;
        
        // 连续阅读设置
        this.continuousReading = true;
        this.maxContinuousChapters = 30;
        
        // 内容解析配置
        this.parseTimeout = 10000; // 10秒超时
        this.retryAttempts = 3;
    }

    async onInit() {
        // 设置消息监听器
        this.setupMessageListeners();
        
        // 设置标签页事件监听器
        this.setupTabEventListeners();
        
        // 加载缓存内容
        await this.loadCachedContent();
        
        // 启动缓存清理定时器
        this.startCacheCleanup();
    }

    // 设置消息监听器
    setupMessageListeners() {
        // 解析当前页面内容
        this.addMessageListener('parseCurrentPage', (message, sender, sendResponse) => {
            this.handleParseCurrentPage(message, sender, sendResponse);
        });

        // 解析指定URL内容
        this.addMessageListener('parseURL', (message, sender, sendResponse) => {
            this.handleParseURL(message, sendResponse);
        });

        // 获取缓存内容
        this.addMessageListener('getCachedContent', (message, sender, sendResponse) => {
            this.handleGetCachedContent(message, sendResponse);
        });

        // 预加载下一章节
        this.addMessageListener('preloadNextChapter', (message, sender, sendResponse) => {
            this.handlePreloadNextChapter(message, sendResponse);
        });

        // 清理缓存
        this.addMessageListener('clearCache', (message, sender, sendResponse) => {
            this.handleClearCache(message, sendResponse);
        });

        // 设置连续阅读
        this.addMessageListener('setContinuousReading', (message, sender, sendResponse) => {
            this.handleSetContinuousReading(message, sendResponse);
        });

        // 获取内容统计
        this.addMessageListener('getContentStats', (message, sender, sendResponse) => {
            this.handleGetContentStats(message, sendResponse);
        });
    }

    // 设置标签页事件监听器
    setupTabEventListeners() {
        // 标签页关闭时清理缓存
        this.addEventListener(chrome.tabs.onRemoved, 'addListener', (tabId, removeInfo) => {
            this.handleTabRemoved(tabId);
        });

        // 标签页更新时清理相关缓存
        this.addEventListener(chrome.tabs.onUpdated, 'addListener', (tabId, changeInfo, tab) => {
            if (changeInfo.url) {
                this.handleTabUrlChanged(tabId, changeInfo.url);
            }
        });
    }

    // 处理解析当前页面
    async handleParseCurrentPage(message, sender, sendResponse) {
        try {
            const tabId = sender.tab?.id;
            if (!tabId) {
                throw new Error('无法获取标签页ID');
            }

            this.log(`开始解析标签页 ${tabId} 的内容`);
            
            // 注入内容脚本并解析
            const content = await this.parseTabContent(tabId);
            
            // 缓存内容
            this.cacheContent(tabId, content);
            
            // 更新当前阅读状态
            this.currentReadingTabId = tabId;
            this.currentArticle = content;
            
            sendResponse({ 
                success: true, 
                content: content,
                tabId: tabId
            });

        } catch (error) {
            this.error("解析当前页面失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理解析指定URL
    async handleParseURL(message, sendResponse) {
        try {
            const { url } = message;
            
            if (!url) {
                throw new Error('URL参数缺失');
            }

            this.log(`开始解析URL: ${url}`);
            
            // 创建新标签页或使用现有标签页
            const tab = await this.createOrFindTab(url);
            
            // 等待页面加载完成
            await this.waitForTabLoad(tab.id);
            
            // 解析内容
            const content = await this.parseTabContent(tab.id);
            
            // 缓存内容
            this.cacheContent(tab.id, content);
            
            // 更新当前阅读状态
            this.currentReadingTabId = tab.id;
            this.currentArticle = content;
            
            sendResponse({ 
                success: true, 
                content: content,
                tabId: tab.id,
                url: url
            });

        } catch (error) {
            this.error("解析URL失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理获取缓存内容
    handleGetCachedContent(message, sendResponse) {
        try {
            const { tabId } = message;
            
            if (tabId && this.tabContentCache.has(tabId)) {
                const content = this.tabContentCache.get(tabId);
                sendResponse({ success: true, content: content });
            } else {
                sendResponse({ success: false, error: '缓存内容不存在' });
            }

        } catch (error) {
            this.error("获取缓存内容失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理预加载下一章节
    async handlePreloadNextChapter(message, sendResponse) {
        try {
            if (!this.continuousReading) {
                sendResponse({ success: false, error: '连续阅读未启用' });
                return;
            }

            const { currentUrl } = message;
            
            // 查找下一章节链接
            const nextChapterUrl = await this.findNextChapterUrl(currentUrl);
            
            if (!nextChapterUrl) {
                sendResponse({ success: false, error: '未找到下一章节' });
                return;
            }

            // 预加载下一章节内容
            const content = await this.preloadChapterContent(nextChapterUrl);
            
            this.nextChapterInfo = {
                url: nextChapterUrl,
                content: content,
                timestamp: Date.now()
            };

            sendResponse({ 
                success: true, 
                nextChapterUrl: nextChapterUrl,
                preloaded: true
            });

        } catch (error) {
            this.error("预加载下一章节失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理清理缓存
    handleClearCache(message, sendResponse) {
        try {
            const { tabId, all } = message;
            
            if (all) {
                this.tabContentCache.clear();
                this.preloadedChapters = [];
                this.log("已清理所有缓存");
            } else if (tabId) {
                this.tabContentCache.delete(tabId);
                this.log(`已清理标签页 ${tabId} 的缓存`);
            }

            sendResponse({ success: true });

        } catch (error) {
            this.error("清理缓存失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理设置连续阅读
    handleSetContinuousReading(message, sendResponse) {
        try {
            const { enabled, maxChapters } = message;
            
            this.continuousReading = enabled;
            
            if (maxChapters !== undefined) {
                this.maxContinuousChapters = Math.max(1, Math.min(100, maxChapters));
            }

            // 保存设置
            this.setStorage('continuousReading', this.continuousReading);
            this.setStorage('maxContinuousChapters', this.maxContinuousChapters);

            this.log(`连续阅读已${enabled ? '启用' : '禁用'}，最大章节数: ${this.maxContinuousChapters}`);
            
            sendResponse({ 
                success: true, 
                continuousReading: this.continuousReading,
                maxChapters: this.maxContinuousChapters
            });

        } catch (error) {
            this.error("设置连续阅读失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理获取内容统计
    handleGetContentStats(message, sendResponse) {
        try {
            const stats = {
                cachedPages: this.tabContentCache.size,
                preloadedChapters: this.preloadedChapters.length,
                currentReadingTabId: this.currentReadingTabId,
                currentChapterCount: this.currentChapterCount,
                continuousReading: this.continuousReading,
                maxContinuousChapters: this.maxContinuousChapters,
                cacheMemoryUsage: this.calculateCacheMemoryUsage()
            };

            sendResponse({ success: true, stats: stats });

        } catch (error) {
            this.error("获取内容统计失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 解析标签页内容
    async parseTabContent(tabId) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('内容解析超时'));
            }, this.parseTimeout);

            // 注入内容脚本
            chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['src/content/parser.js']
            }, (results) => {
                clearTimeout(timeout);
                
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                    return;
                }

                if (!results || results.length === 0) {
                    reject(new Error('内容脚本执行失败'));
                    return;
                }

                // 发送解析消息
                chrome.tabs.sendMessage(tabId, { action: 'parseContent' }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }

                    if (response && response.success) {
                        resolve(response.content);
                    } else {
                        reject(new Error(response?.error || '内容解析失败'));
                    }
                });
            });
        });
    }

    // 创建或查找标签页
    async createOrFindTab(url) {
        return new Promise((resolve, reject) => {
            // 首先查找是否已有相同URL的标签页
            chrome.tabs.query({ url: url }, (tabs) => {
                if (tabs && tabs.length > 0) {
                    // 使用现有标签页
                    resolve(tabs[0]);
                } else {
                    // 创建新标签页
                    chrome.tabs.create({ url: url, active: false }, (tab) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(tab);
                        }
                    });
                }
            });
        });
    }

    // 等待标签页加载完成
    async waitForTabLoad(tabId) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('页面加载超时'));
            }, 30000); // 30秒超时

            const checkStatus = () => {
                chrome.tabs.get(tabId, (tab) => {
                    if (chrome.runtime.lastError) {
                        clearTimeout(timeout);
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }

                    if (tab.status === 'complete') {
                        clearTimeout(timeout);
                        resolve();
                    } else {
                        setTimeout(checkStatus, 1000);
                    }
                });
            };

            checkStatus();
        });
    }

    // 查找下一章节URL
    async findNextChapterUrl(currentUrl) {
        // 这里需要实现智能的下一章节查找逻辑
        // 可以基于页面结构、URL模式等进行判断
        
        try {
            // 获取当前页面的下一章节链接
            const tabId = this.currentReadingTabId;
            if (!tabId) return null;

            return new Promise((resolve) => {
                chrome.tabs.sendMessage(tabId, { 
                    action: 'findNextChapter',
                    currentUrl: currentUrl
                }, (response) => {
                    if (response && response.success) {
                        resolve(response.nextUrl);
                    } else {
                        resolve(null);
                    }
                });
            });

        } catch (error) {
            this.error("查找下一章节失败:", error);
            return null;
        }
    }

    // 预加载章节内容
    async preloadChapterContent(url) {
        try {
            const tab = await this.createOrFindTab(url);
            await this.waitForTabLoad(tab.id);
            const content = await this.parseTabContent(tab.id);
            
            // 添加到预加载列表
            this.preloadedChapters.push({
                url: url,
                content: content,
                timestamp: Date.now()
            });

            return content;

        } catch (error) {
            this.error("预加载章节内容失败:", error);
            throw error;
        }
    }

    // 缓存内容
    cacheContent(tabId, content) {
        // 检查缓存大小限制
        if (this.tabContentCache.size >= this.maxCacheSize) {
            // 删除最旧的缓存项
            const oldestKey = this.tabContentCache.keys().next().value;
            this.tabContentCache.delete(oldestKey);
        }

        this.tabContentCache.set(tabId, {
            ...content,
            cachedAt: Date.now()
        });

        this.log(`已缓存标签页 ${tabId} 的内容`);
    }

    // 处理标签页关闭
    handleTabRemoved(tabId) {
        if (this.tabContentCache.has(tabId)) {
            this.tabContentCache.delete(tabId);
            this.log(`标签页 ${tabId} 关闭，已清理缓存`);
        }

        if (this.currentReadingTabId === tabId) {
            this.currentReadingTabId = null;
            this.currentArticle = null;
        }
    }

    // 处理标签页URL变化
    handleTabUrlChanged(tabId, newUrl) {
        if (this.tabContentCache.has(tabId)) {
            this.tabContentCache.delete(tabId);
            this.log(`标签页 ${tabId} URL变化，已清理缓存`);
        }
    }

    // 启动缓存清理
    startCacheCleanup() {
        // 每5分钟清理一次过期缓存
        this.createTimer(() => {
            this.cleanupExpiredCache();
        }, 5 * 60 * 1000, false);
    }

    // 清理过期缓存
    cleanupExpiredCache() {
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30分钟

        // 清理内容缓存
        for (const [tabId, content] of this.tabContentCache) {
            if (now - content.cachedAt > maxAge) {
                this.tabContentCache.delete(tabId);
            }
        }

        // 清理预加载缓存
        this.preloadedChapters = this.preloadedChapters.filter(chapter => 
            now - chapter.timestamp < maxAge
        );

        this.log("缓存清理完成");
    }

    // 计算缓存内存使用量
    calculateCacheMemoryUsage() {
        let totalSize = 0;
        
        for (const content of this.tabContentCache.values()) {
            totalSize += JSON.stringify(content).length;
        }
        
        for (const chapter of this.preloadedChapters) {
            totalSize += JSON.stringify(chapter).length;
        }
        
        return Math.round(totalSize / 1024); // 返回KB
    }

    // 加载缓存内容
    async loadCachedContent() {
        try {
            const settings = await Promise.all([
                this.getStorage('continuousReading', true),
                this.getStorage('maxContinuousChapters', 30)
            ]);

            this.continuousReading = settings[0];
            this.maxContinuousChapters = settings[1];

            this.log("缓存设置已加载");

        } catch (error) {
            this.error("加载缓存设置失败:", error);
        }
    }

    // 获取状态
    getStatus() {
        return {
            ...super.getStatus(),
            cachedPages: this.tabContentCache.size,
            preloadedChapters: this.preloadedChapters.length,
            currentReadingTabId: this.currentReadingTabId,
            continuousReading: this.continuousReading,
            maxContinuousChapters: this.maxContinuousChapters,
            cacheMemoryUsage: this.calculateCacheMemoryUsage()
        };
    }

    // 公共API
    getCurrentContent() {
        return this.currentArticle;
    }

    getCurrentTabId() {
        return this.currentReadingTabId;
    }

    getCachedContent(tabId) {
        return this.tabContentCache.get(tabId);
    }

    clearAllCache() {
        this.tabContentCache.clear();
        this.preloadedChapters = [];
        this.nextChapterInfo = null;
    }
}
