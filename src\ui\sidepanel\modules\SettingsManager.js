// 设置管理模块 - 处理设置面板、语音设置、偏好设置等功能
import { BaseModule } from './BaseModule.js';

export class SettingsManager extends BaseModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // 设置状态
        this.settings = {
            defaultVoice: null,
            favoriteVoices: [],
            voiceFilters: {
                gender: [],
                language: 'all'
            },
            playbackSettings: {
                speed: 1.1,
                continuousReading: true,
                continuousChapters: 30,
                readingDuration: 30 * 60
            }
        };
        
        // DOM元素引用
        this.settingsBtn = null;
        this.settingsOverlay = null;
        this.closeSettingsBtn = null;
        this.overlaySpeedSlider = null;
        this.overlaySpeedValue = null;
        this.overlayContinuousToggle = null;
        this.saveVoicesSettingsBtn = null;
        this.manageVoicesBtn = null;
        this.quickManageVoicesBtn = null;
        
        // 章节卡片相关
        this.chapterCardHeader = null;
        this.toggleChapterCardBtn = null;
        this.chapterDetails = null;
        this.currentChapterTitleBrief = null;
        this.currentChapterTitleEl = null;
        
        // 状态
        this.isSettingsOpen = false;
        this.isChapterDetailsExpanded = true;
    }

    async onInit() {
        // 获取DOM元素
        this.initDOMElements();
        
        // 设置事件监听器
        this.setupEventListeners();
        
        // 加载设置
        await this.loadSettings();
        
        // 初始化UI
        this.initializeSettingsUI();
    }

    initDOMElements() {
        this.settingsBtn = document.getElementById('voice-settings-btn');
        this.settingsOverlay = document.getElementById('settings-overlay');
        this.closeSettingsBtn = document.getElementById('close-settings-btn');
        this.overlaySpeedSlider = document.getElementById('overlay-speed-slider');
        this.overlaySpeedValue = document.getElementById('overlay-speed-value');
        this.overlayContinuousToggle = document.getElementById('overlay-continuous-toggle');
        this.saveVoicesSettingsBtn = document.getElementById('save-voices-settings');
        this.manageVoicesBtn = document.getElementById('manage-voices-btn');
        this.quickManageVoicesBtn = document.getElementById('quick-manage-voices');
        
        // 章节卡片元素
        this.chapterCardHeader = document.querySelector('.card-header-collapsible');
        this.toggleChapterCardBtn = document.querySelector('.toggle-chapter-card');
        this.chapterDetails = document.querySelector('.chapter-details');
        this.currentChapterTitleBrief = document.getElementById('current-chapter-title-brief');
        this.currentChapterTitleEl = document.getElementById('current-chapter-title');
    }

    setupEventListeners() {
        // 设置按钮
        if (this.settingsBtn) {
            this.addEventListener(this.settingsBtn, 'click', () => {
                this.showSettingsOverlay();
            });
        }

        // 关闭设置按钮
        if (this.closeSettingsBtn) {
            this.addEventListener(this.closeSettingsBtn, 'click', () => {
                this.hideSettingsOverlay();
            });
        }

        // 设置覆盖层点击关闭
        if (this.settingsOverlay) {
            this.addEventListener(this.settingsOverlay, 'click', (e) => {
                if (e.target === this.settingsOverlay) {
                    this.hideSettingsOverlay();
                }
            });
        }

        // 覆盖层速度滑块
        if (this.overlaySpeedSlider) {
            this.addEventListener(this.overlaySpeedSlider, 'input', (e) => {
                this.handleOverlaySpeedChange(parseFloat(e.target.value));
            });
        }

        // 覆盖层连续阅读切换
        if (this.overlayContinuousToggle) {
            this.addEventListener(this.overlayContinuousToggle, 'change', (e) => {
                this.handleOverlayContinuousToggle(e.target.checked);
            });
        }

        // 保存语音设置按钮
        if (this.saveVoicesSettingsBtn) {
            this.addEventListener(this.saveVoicesSettingsBtn, 'click', () => {
                this.saveVoiceSettings();
            });
        }

        // 管理语音按钮
        if (this.manageVoicesBtn) {
            this.addEventListener(this.manageVoicesBtn, 'click', () => {
                this.openVoiceManagement();
            });
        }

        if (this.quickManageVoicesBtn) {
            this.addEventListener(this.quickManageVoicesBtn, 'click', () => {
                this.openVoiceManagement();
            });
        }

        // 章节卡片切换
        if (this.chapterCardHeader) {
            this.addEventListener(this.chapterCardHeader, 'click', () => {
                this.toggleChapterDetails();
            });
        }

        if (this.toggleChapterCardBtn) {
            this.addEventListener(this.toggleChapterCardBtn, 'click', () => {
                this.toggleChapterDetails();
            });
        }

        // ESC键关闭设置
        this.addEventListener(document, 'keydown', (e) => {
            if (e.key === 'Escape' && this.isSettingsOpen) {
                this.hideSettingsOverlay();
            }
        });
    }

    // 加载设置
    async loadSettings() {
        try {
            const result = await this.dependencies.chrome.storage.local.get([
                'defaultVoice',
                'favoriteVoices',
                'voiceFilters',
                'playbackSettings'
            ]);
            
            this.settings = {
                defaultVoice: result.defaultVoice || null,
                favoriteVoices: result.favoriteVoices || [],
                voiceFilters: result.voiceFilters || {
                    gender: [],
                    language: 'all'
                },
                playbackSettings: result.playbackSettings || {
                    speed: 1.1,
                    continuousReading: true,
                    continuousChapters: 30,
                    readingDuration: 30 * 60
                }
            };
            
            console.log('设置已加载:', this.settings);
        } catch (error) {
            this.errorHandler.handleError(error, 'load_settings');
        }
    }

    // 保存设置
    async saveSettings() {
        try {
            await this.dependencies.chrome.storage.local.set({
                defaultVoice: this.settings.defaultVoice,
                favoriteVoices: this.settings.favoriteVoices,
                voiceFilters: this.settings.voiceFilters,
                playbackSettings: this.settings.playbackSettings
            });
            
            console.log('设置已保存');
        } catch (error) {
            this.errorHandler.handleError(error, 'save_settings');
        }
    }

    // 显示设置覆盖层
    showSettingsOverlay() {
        if (!this.settingsOverlay) return;
        
        this.isSettingsOpen = true;
        this.settingsOverlay.style.display = 'flex';
        
        // 更新覆盖层中的设置值
        this.updateOverlaySettings();
        
        // 添加动画类
        setTimeout(() => {
            this.settingsOverlay.classList.add('show');
        }, 10);
        
        console.log('设置面板已打开');
    }

    // 隐藏设置覆盖层
    hideSettingsOverlay() {
        if (!this.settingsOverlay) return;
        
        this.isSettingsOpen = false;
        this.settingsOverlay.classList.remove('show');
        
        // 延迟隐藏以等待动画完成
        setTimeout(() => {
            this.settingsOverlay.style.display = 'none';
        }, 300);
        
        console.log('设置面板已关闭');
    }

    // 更新覆盖层设置
    updateOverlaySettings() {
        // 更新速度滑块
        if (this.overlaySpeedSlider) {
            this.overlaySpeedSlider.value = this.settings.playbackSettings.speed;
        }
        
        if (this.overlaySpeedValue) {
            this.overlaySpeedValue.textContent = this.settings.playbackSettings.speed.toFixed(1) + 'x';
        }
        
        // 更新连续阅读切换
        if (this.overlayContinuousToggle) {
            this.overlayContinuousToggle.checked = this.settings.playbackSettings.continuousReading;
        }
    }

    // 处理覆盖层速度变化
    handleOverlaySpeedChange(speed) {
        this.settings.playbackSettings.speed = speed;
        
        if (this.overlaySpeedValue) {
            this.overlaySpeedValue.textContent = speed.toFixed(1) + 'x';
        }
        
        // 实时更新播放速度
        this.dependencies.chrome.runtime.sendMessage({
            action: 'setSpeed',
            speed: speed
        });
        
        // 通知其他模块速度变化
        this.notifySpeedChange(speed);
    }

    // 处理覆盖层连续阅读切换
    handleOverlayContinuousToggle(enabled) {
        this.settings.playbackSettings.continuousReading = enabled;
        
        // 实时更新连续阅读设置
        this.dependencies.chrome.runtime.sendMessage({
            action: 'setContinuousReading',
            enabled: enabled
        });
        
        // 通知其他模块连续阅读设置变化
        this.notifyContinuousReadingChange(enabled);
    }

    // 保存语音设置
    saveVoiceSettings() {
        this.saveSettings();
        
        // 发送语音设置更新到后台
        this.dependencies.chrome.runtime.sendMessage({
            action: 'updateVoiceSettings',
            defaultVoice: this.settings.defaultVoice,
            favoriteVoices: this.settings.favoriteVoices,
            voiceFilters: this.settings.voiceFilters
        });
        
        // 显示保存成功消息
        this.showSaveSuccessMessage();
        
        console.log('语音设置已保存');
    }

    // 显示保存成功消息
    showSaveSuccessMessage() {
        // 创建临时消息元素
        const message = document.createElement('div');
        message.className = 'save-success-message';
        message.textContent = '设置已保存';
        message.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 10001;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;
        
        document.body.appendChild(message);
        
        // 3秒后移除
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 3000);
    }

    // 打开语音管理
    openVoiceManagement() {
        // 通知语音管理器打开语音设置
        const voiceManager = this.dependencies.moduleContainer.getModule('VoiceManager');
        if (voiceManager) {
            // 这里可以触发语音管理器的特定功能
            console.log('打开语音管理界面');
        }
        
        // 或者打开专门的语音设置页面
        this.dependencies.chrome.tabs.create({
            url: this.dependencies.chrome.runtime.getURL('src/ui/voiceSettings/index.html')
        });
    }

    // 切换章节详情
    toggleChapterDetails() {
        this.isChapterDetailsExpanded = !this.isChapterDetailsExpanded;
        
        if (this.chapterDetails) {
            this.chapterDetails.style.display = this.isChapterDetailsExpanded ? 'block' : 'none';
        }
        
        if (this.toggleChapterCardBtn) {
            this.toggleChapterCardBtn.textContent = this.isChapterDetailsExpanded ? '▼' : '▶';
        }
        
        // 更新简要标题显示
        this.updateChapterTitleDisplay();
        
        console.log('章节详情', this.isChapterDetailsExpanded ? '展开' : '折叠');
    }

    // 更新章节标题显示
    updateChapterTitleDisplay() {
        if (!this.currentChapterTitleEl || !this.currentChapterTitleBrief) return;
        
        const fullTitle = this.currentChapterTitleEl.textContent;
        
        if (this.isChapterDetailsExpanded) {
            // 展开时隐藏简要标题
            this.currentChapterTitleBrief.style.display = 'none';
        } else {
            // 折叠时显示简要标题
            this.currentChapterTitleBrief.style.display = 'block';
            this.currentChapterTitleBrief.textContent = this.processChapterTitle(fullTitle);
        }
    }

    // 处理章节标题，遇到下划线就截断
    processChapterTitle(title) {
        if (!title) return title;
        
        // 查找第一个下划线的位置
        const underscoreIndex = title.indexOf('_');
        
        // 如果标题中包含下划线，则只保留下划线之前的内容
        if (underscoreIndex !== -1) {
            return title.substring(0, underscoreIndex).trim();
        }
        
        // 如果没有下划线，则返回原标题
        return title;
    }

    // 更新当前章节标题
    updateCurrentChapterTitle(title) {
        if (this.currentChapterTitleEl) {
            this.currentChapterTitleEl.textContent = title || '未知章节';
        }
        
        // 更新简要标题显示
        this.updateChapterTitleDisplay();
    }

    // 初始化设置UI
    initializeSettingsUI() {
        // 初始化章节详情展开状态
        this.isChapterDetailsExpanded = true;
        this.updateChapterTitleDisplay();
        
        // 更新覆盖层设置
        this.updateOverlaySettings();
    }

    // 通知速度变化
    notifySpeedChange(speed) {
        // 通知播放控制器更新速度显示
        const playbackController = this.dependencies.moduleContainer.getModule('PlaybackController');
        if (playbackController && playbackController.updateSpeed) {
            playbackController.updateSpeed(speed);
        }
    }

    // 通知连续阅读设置变化
    notifyContinuousReadingChange(enabled) {
        // 通知播放控制器更新连续阅读设置
        const playbackController = this.dependencies.moduleContainer.getModule('PlaybackController');
        if (playbackController && playbackController.handleContinuousReadingToggle) {
            playbackController.handleContinuousReadingToggle(enabled);
        }
    }

    // 公共API
    getSettings() {
        return { ...this.settings };
    }

    updateSetting(key, value) {
        if (key.includes('.')) {
            // 处理嵌套属性
            const keys = key.split('.');
            let obj = this.settings;
            for (let i = 0; i < keys.length - 1; i++) {
                obj = obj[keys[i]];
            }
            obj[keys[keys.length - 1]] = value;
        } else {
            this.settings[key] = value;
        }
        
        this.saveSettings();
    }

    isSettingsOverlayOpen() {
        return this.isSettingsOpen;
    }

    closeSettings() {
        this.hideSettingsOverlay();
    }

    openSettings() {
        this.showSettingsOverlay();
    }

    // 设置默认语音
    setDefaultVoice(voiceName) {
        this.settings.defaultVoice = voiceName;
        this.saveSettings();
    }

    // 更新收藏语音
    updateFavoriteVoices(favoriteVoices) {
        this.settings.favoriteVoices = favoriteVoices;
        this.saveSettings();
    }

    // 更新语音过滤器
    updateVoiceFilters(filters) {
        this.settings.voiceFilters = { ...this.settings.voiceFilters, ...filters };
        this.saveSettings();
    }

    // 更新播放设置
    updatePlaybackSettings(settings) {
        this.settings.playbackSettings = { ...this.settings.playbackSettings, ...settings };
        this.saveSettings();
    }
}
