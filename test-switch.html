<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开关测试</title>
    <link rel="stylesheet" href="src/ui/sidepanel/compact-styles.css">
    <style>
        body {
            background: #1a1a2e;
            color: #F0F0F5;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        .test-item {
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 5px;
            border: 1px dashed rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }
        .test-label {
            font-size: 14px;
            min-width: 120px;
        }
        /* 添加高度参考线 */
        .test-item::after {
            content: '';
            position: absolute;
            right: 10px;
            width: 2px;
            height: 36px;
            background: rgba(225, 173, 91, 0.3);
            border-radius: 1px;
        }
        .test-item {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>UI元素高度统一测试</h2>

        <!-- 四个元素高度测试 -->
        <div class="test-item">
            <span class="test-label">时间显示:</span>
            <div id="playback-timer" class="playback-timer">00:00:00</div>
        </div>

        <div class="test-item">
            <span class="test-label">播放按钮:</span>
            <button id="play-pause-btn" class="control-btn main-btn" title="播放">
                <span class="icon-play">▶</span>
            </button>
        </div>

        <div class="test-item">
            <span class="test-label">停止按钮:</span>
            <button id="stop-btn" class="control-btn small-btn" title="停止">⏹</button>
        </div>

        <div class="test-item">
            <span class="test-label">状态显示:</span>
            <div id="status-indicator" class="status-indicator idle">空闲</div>
        </div>

        <hr style="margin: 20px 0; border-color: rgba(255,255,255,0.2);">

        <!-- 连体按钮组测试 -->
        <h3>连体按钮组测试</h3>

        <div class="test-item">
            <span class="test-label">语速控制:</span>
            <div class="control-group">
                <button class="control-btn-left">-</button>
                <span class="control-display">1.7x</span>
                <button class="control-btn-right">+</button>
            </div>
        </div>

        <div class="test-item">
            <span class="test-label">章节控制:</span>
            <div class="control-group">
                <button class="control-btn-left">-</button>
                <span class="control-display">48</span>
                <button class="control-btn-right">+</button>
            </div>
        </div>

        <!-- 开关测试 -->
        <h3>连续播放开关测试</h3>

        <div class="test-item">
            <span class="test-label">开关 (关闭):</span>
            <label class="switch">
                <input type="checkbox" id="test-toggle-1">
                <span class="slider round"></span>
            </label>
        </div>

        <div class="test-item">
            <span class="test-label">开关 (开启):</span>
            <label class="switch">
                <input type="checkbox" id="test-toggle-2" checked>
                <span class="slider round"></span>
            </label>
        </div>
        
        <p style="margin-top: 30px; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
            <strong>高度统一修正：</strong><br>
            • 时间显示、播放按钮、停止按钮、状态显示：统一高度36px<br>
            • 使用flex布局垂直居中对齐<br>
            • 响应式设计下统一高度32px<br><br>
            <strong>连体按钮组设计：</strong><br>
            • 整体高度：26px（与连续播放开关一致）<br>
            • 圆角半径：13px（与连续播放开关一致）<br>
            • 无缝连接的三段式设计<br>
            • 中间显示区域橙黄色背景突出重要性<br>
            • +- 按钮字体放大30%（16px）<br>
            • 三个容器固定高度60px确保一致性<br>
            • +- 按钮完全垂直居中对齐<br><br>
            <strong>开关修正：</strong><br>
            • 开关高度：26px（足够容纳20px圆圈）<br>
            • 黄色背景高度：26px（完全包含圆圈）<br>
            • 圆圈垂直居中定位<br>
            • 圆角半径：13px（高度的一半）
        </p>
    </div>
    
    <script>
        // 连体按钮组交互测试
        document.addEventListener('DOMContentLoaded', function() {
            // 语速控制测试
            const speedControls = document.querySelectorAll('.control-group')[0];
            const speedDisplay = speedControls.querySelector('.control-display');
            const speedDecrease = speedControls.querySelector('.control-btn-left');
            const speedIncrease = speedControls.querySelector('.control-btn-right');

            let currentSpeed = 1.7;

            speedDecrease.addEventListener('click', function() {
                currentSpeed = Math.max(0.5, currentSpeed - 0.1);
                speedDisplay.textContent = currentSpeed.toFixed(1) + 'x';
                console.log('语速调整为:', currentSpeed);
            });

            speedIncrease.addEventListener('click', function() {
                currentSpeed = Math.min(3.0, currentSpeed + 0.1);
                speedDisplay.textContent = currentSpeed.toFixed(1) + 'x';
                console.log('语速调整为:', currentSpeed);
            });

            // 章节控制测试
            const chapterControls = document.querySelectorAll('.control-group')[1];
            const chapterDisplay = chapterControls.querySelector('.control-display');
            const chapterDecrease = chapterControls.querySelector('.control-btn-left');
            const chapterIncrease = chapterControls.querySelector('.control-btn-right');

            let currentChapter = 48;

            chapterDecrease.addEventListener('click', function() {
                currentChapter = Math.max(1, currentChapter - 1);
                chapterDisplay.textContent = currentChapter;
                console.log('章节数调整为:', currentChapter);
            });

            chapterIncrease.addEventListener('click', function() {
                currentChapter = Math.min(999, currentChapter + 1);
                chapterDisplay.textContent = currentChapter;
                console.log('章节数调整为:', currentChapter);
            });
        });
    </script>
</body>
</html>
