// 神灯AI·灵阅 - 可工作的最简版本
console.log("🚀 神灯AI·灵阅 Service Worker 启动");

// 基础状态
let readingState = 'idle';
let currentReadingTabId = null;
let currentSpeed = 1.0;
let tabContentCache = {};

// 插件图标点击 - 打开侧边栏
chrome.action.onClicked.addListener((tab) => {
  console.log("🖱️ 插件图标被点击，标签页:", tab.id);

  chrome.sidePanel.open({ windowId: tab.windowId })
    .then(() => {
      console.log("✅ 侧边栏打开成功");
      currentReadingTabId = tab.id;
    })
    .catch((error) => {
      console.error("❌ 侧边栏打开失败:", error);
    });
});

// 消息处理
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log("📨 收到消息:", message.action);

  try {
    switch (message.action) {
      case "getState":
        sendResponse({
          readingState: readingState,
          currentReadingTabId: currentReadingTabId,
          currentSpeed: currentSpeed
        });
        break;

      case "parsedContent":
        if (sender.tab?.id && message.article) {
          console.log("📄 收到解析内容，标签页:", sender.tab.id);
          console.log("📖 文章标题:", message.article.title);
          console.log("📝 文本长度:", message.article.textContent?.length || 0);

          tabContentCache[sender.tab.id] = message.article;
          currentReadingTabId = sender.tab.id;

          // 如果当前状态是loading，说明是播放触发的解析，自动开始播放
          if (readingState === 'loading' && message.article.textContent) {
            console.log("🎵 解析完成，自动开始播放");
            readingState = 'reading';

            chrome.tts.speak(message.article.textContent, {
              rate: currentSpeed,
              onEvent: (event) => {
                if (event.type === 'end') {
                  readingState = 'idle';
                  console.log("🔚 播放结束");
                }
              }
            });
          }

          sendResponse({ status: "内容已缓存" });
        }
        break;

      case "startReading":
        console.log("▶️ 开始播放请求，发送者标签页:", sender.tab?.id);

        // 确保有当前标签页ID
        const tabId = sender.tab?.id || currentReadingTabId;
        if (!tabId) {
          sendResponse({ error: "无法确定当前标签页" });
          break;
        }

        // 设置当前阅读标签页
        currentReadingTabId = tabId;

        if (tabContentCache[currentReadingTabId]) {
          const article = tabContentCache[currentReadingTabId];
          if (article.textContent) {
            readingState = 'reading';

            // 使用Chrome TTS播放
            chrome.tts.speak(article.textContent, {
              rate: currentSpeed,
              onEvent: (event) => {
                if (event.type === 'end') {
                  readingState = 'idle';
                  console.log("🔚 播放结束");
                }
              }
            });

            sendResponse({ status: "播放已开始" });
          } else {
            sendResponse({ error: "没有文本内容" });
          }
        } else {
          // 没有缓存内容，触发解析
          console.log("📄 没有缓存内容，触发解析，标签页:", currentReadingTabId);
          readingState = 'loading';

          chrome.tabs.sendMessage(currentReadingTabId, { action: 'parseContent' })
            .then(() => {
              console.log("✅ 解析消息已发送");
            })
            .catch(error => {
              console.error("❌ 发送解析消息失败:", error);
              readingState = 'idle';
            });

          sendResponse({ status: "正在解析内容..." });
        }
        break;

      case "pauseReading":
        console.log("⏸️ 暂停播放");
        chrome.tts.pause();
        readingState = 'paused';
        sendResponse({ status: "已暂停" });
        break;

      case "resumeReading":
        console.log("▶️ 继续播放");
        chrome.tts.resume();
        readingState = 'reading';
        sendResponse({ status: "已继续" });
        break;

      case "stopReading":
        console.log("⏹️ 停止播放");
        chrome.tts.stop();
        readingState = 'idle';
        sendResponse({ status: "已停止" });
        break;

      case "setSpeed":
        if (message.speed && message.speed >= 0.5 && message.speed <= 3.0) {
          currentSpeed = message.speed;
          console.log("🎛️ 语速设置为:", currentSpeed);
          sendResponse({ status: "语速已更新" });
        } else {
          sendResponse({ error: "无效的语速值" });
        }
        break;

      default:
        sendResponse({ error: "未知操作: " + message.action });
        break;
    }
  } catch (error) {
    console.error("❌ 消息处理错误:", error);
    sendResponse({ error: "处理消息时出错: " + error.message });
  }
});

console.log("✅ 神灯AI·灵阅 Service Worker 初始化完成");
