# 停止按钮播放功能修复测试

## 问题描述
在播放状态下，点击停止按钮后，再次点击播放功能失效。

## 修复内容

### 1. 修改停止按钮逻辑 (src/background/index.js)
- 停止按钮触发时，状态设置为 'stopped' 而不是 'idle'
- 保留 currentReadingTabId 和 tabContentCache，不清空
- 添加详细日志记录

### 2. 添加从停止状态重新播放的逻辑 (src/background/index.js)
- 在 startReading 处理中添加对 'stopped' 状态的检查
- 从停止状态重新播放时，总是从头开始
- 确保正确的状态转换和计时器管理

### 3. 更新UI控制器 (src/ui/sidepanel/modules/UIController.js)
- 播放按钮在 'stopped' 状态下显示 "重新开始播放"
- 停止按钮在 'stopped' 状态下被禁用
- 状态翻译映射已包含 'stopped' 状态

## 测试步骤

1. 打开一个网页
2. 点击播放按钮开始朗读
3. 点击停止按钮停止朗读
4. 再次点击播放按钮
5. 验证是否能正常重新开始播放

## 预期结果
- 停止后再次点击播放应该能正常工作
- UI状态应该正确显示
- 不应该出现功能失效的问题

## 关键修改点
1. 停止状态使用 'stopped' 而不是 'idle'
2. 保留必要的缓存和标签页信息
3. 添加专门的停止状态重新播放逻辑
4. 更新UI状态处理逻辑
