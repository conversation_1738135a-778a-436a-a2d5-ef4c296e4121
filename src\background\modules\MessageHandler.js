// 消息处理模块 - 处理前端与后台的消息通信
import { BaseBackgroundModule } from './BaseBackgroundModule.js';

export class MessageHandler extends BaseBackgroundModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // 消息路由表
        this.messageRoutes = new Map();
        
        // 消息统计
        this.messageStats = {
            total: 0,
            success: 0,
            error: 0,
            byAction: new Map()
        };
        
        // 消息队列（用于处理高频消息）
        this.messageQueue = [];
        this.isProcessingQueue = false;
        this.maxQueueSize = 100;
        
        // 响应超时设置
        this.responseTimeout = 10000; // 10秒
    }

    async onInit() {
        // 设置主消息监听器
        this.setupMainMessageListener();
        
        // 注册核心消息路由
        this.registerCoreRoutes();
        
        // 启动消息队列处理
        this.startMessageQueueProcessor();
        
        // 设置消息统计定时器
        this.startMessageStatsLogger();
    }

    // 设置主消息监听器
    setupMainMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            return this.handleMessage(message, sender, sendResponse);
        });
        
        this.log("主消息监听器已设置");
    }

    // 注册核心消息路由
    registerCoreRoutes() {
        // 状态相关
        this.registerRoute('getState', this.handleGetState.bind(this));
        this.registerRoute('setState', this.handleSetState.bind(this));
        
        // 系统相关
        this.registerRoute('ping', this.handlePing.bind(this));
        this.registerRoute('getSystemInfo', this.handleGetSystemInfo.bind(this));
        
        // 侧边栏相关
        this.registerRoute('sidePanelClosing', this.handleSidePanelClosing.bind(this));
        this.registerRoute('sidePanelOpened', this.handleSidePanelOpened.bind(this));
        
        // 错误报告
        this.registerRoute('reportError', this.handleReportError.bind(this));
        
        this.log("核心消息路由已注册");
    }

    // 注册消息路由
    registerRoute(action, handler, options = {}) {
        this.messageRoutes.set(action, {
            handler: handler,
            options: options,
            registeredAt: Date.now()
        });
        
        this.log(`注册消息路由: ${action}`);
    }

    // 取消注册消息路由
    unregisterRoute(action) {
        if (this.messageRoutes.has(action)) {
            this.messageRoutes.delete(action);
            this.log(`取消注册消息路由: ${action}`);
            return true;
        }
        return false;
    }

    // 处理消息
    handleMessage(message, sender, sendResponse) {
        try {
            // 更新统计
            this.updateMessageStats(message.action);
            
            // 验证消息格式
            if (!this.validateMessage(message)) {
                this.sendErrorResponse(sendResponse, '消息格式无效');
                return false;
            }

            // 检查是否有对应的路由
            const route = this.messageRoutes.get(message.action);
            if (!route) {
                // 尝试转发到其他模块
                return this.forwardMessageToModule(message, sender, sendResponse);
            }

            // 执行路由处理器
            const result = route.handler(message, sender, sendResponse);
            
            // 如果处理器返回Promise，等待结果
            if (result instanceof Promise) {
                result.then(response => {
                    if (response !== undefined) {
                        sendResponse(response);
                    }
                }).catch(error => {
                    this.error(`消息处理异步错误 (${message.action}):`, error);
                    this.sendErrorResponse(sendResponse, error.message);
                });
                return true; // 保持消息通道开放
            }

            // 同步处理完成
            return result !== undefined;

        } catch (error) {
            this.error(`消息处理错误 (${message.action}):`, error);
            this.sendErrorResponse(sendResponse, error.message);
            return false;
        }
    }

    // 验证消息格式
    validateMessage(message) {
        return message && 
               typeof message === 'object' && 
               typeof message.action === 'string' && 
               message.action.length > 0;
    }

    // 转发消息到其他模块
    forwardMessageToModule(message, sender, sendResponse) {
        const { action } = message;
        
        // 根据消息类型转发到相应模块
        let targetModule = null;
        
        if (action.includes('tts') || action.includes('voice') || action.includes('speak')) {
            targetModule = this.dependencies.moduleContainer?.getModule('TTSManager');
        } else if (action.includes('content') || action.includes('parse') || action.includes('cache')) {
            targetModule = this.dependencies.moduleContainer?.getModule('ContentManager');
        } else if (action.includes('state') || action.includes('storage')) {
            targetModule = this.dependencies.moduleContainer?.getModule('StateManager');
        }

        if (targetModule && typeof targetModule.handleMessage === 'function') {
            try {
                return targetModule.handleMessage(message, sender, sendResponse);
            } catch (error) {
                this.error(`转发消息到模块失败 (${action}):`, error);
            }
        }

        // 未找到处理器
        this.warn(`未找到消息处理器: ${action}`);
        this.sendErrorResponse(sendResponse, `未知的消息类型: ${action}`);
        return false;
    }

    // 发送错误响应
    sendErrorResponse(sendResponse, errorMessage) {
        if (sendResponse) {
            sendResponse({
                success: false,
                error: errorMessage,
                timestamp: Date.now()
            });
        }
        this.messageStats.error++;
    }

    // 发送成功响应
    sendSuccessResponse(sendResponse, data = {}) {
        if (sendResponse) {
            sendResponse({
                success: true,
                ...data,
                timestamp: Date.now()
            });
        }
        this.messageStats.success++;
    }

    // 更新消息统计
    updateMessageStats(action) {
        this.messageStats.total++;
        
        if (this.messageStats.byAction.has(action)) {
            this.messageStats.byAction.set(action, this.messageStats.byAction.get(action) + 1);
        } else {
            this.messageStats.byAction.set(action, 1);
        }
    }

    // 核心消息处理器
    handleGetState(message, sender, sendResponse) {
        try {
            const stateManager = this.dependencies.moduleContainer?.getModule('StateManager');
            if (stateManager) {
                const state = stateManager.getCurrentState();
                this.sendSuccessResponse(sendResponse, { state });
            } else {
                this.sendErrorResponse(sendResponse, 'StateManager不可用');
            }
        } catch (error) {
            this.sendErrorResponse(sendResponse, error.message);
        }
    }

    handleSetState(message, sender, sendResponse) {
        try {
            const { state } = message;
            const stateManager = this.dependencies.moduleContainer?.getModule('StateManager');
            
            if (stateManager) {
                stateManager.updateState(state);
                this.sendSuccessResponse(sendResponse, { updated: true });
            } else {
                this.sendErrorResponse(sendResponse, 'StateManager不可用');
            }
        } catch (error) {
            this.sendErrorResponse(sendResponse, error.message);
        }
    }

    handlePing(message, sender, sendResponse) {
        this.sendSuccessResponse(sendResponse, { 
            status: 'pong',
            timestamp: Date.now(),
            version: '0.52'
        });
    }

    handleGetSystemInfo(message, sender, sendResponse) {
        try {
            const systemInfo = {
                version: '0.52',
                modules: this.dependencies.moduleContainer?.getModuleNames() || [],
                messageStats: this.messageStats,
                uptime: Date.now() - (this.dependencies.startTime || Date.now()),
                memory: this.getMemoryUsage()
            };
            
            this.sendSuccessResponse(sendResponse, { systemInfo });
        } catch (error) {
            this.sendErrorResponse(sendResponse, error.message);
        }
    }

    handleSidePanelClosing(message, sender, sendResponse) {
        try {
            this.log("收到侧边栏关闭消息");
            
            // 通知所有模块侧边栏即将关闭
            this.notifyModules('sidePanelClosing', message);
            
            // 停止TTS播放
            const ttsManager = this.dependencies.moduleContainer?.getModule('TTSManager');
            if (ttsManager) {
                ttsManager.stop().catch(error => {
                    this.error("停止TTS失败:", error);
                });
            }
            
            this.sendSuccessResponse(sendResponse, { message: '侧边栏关闭处理完成' });
        } catch (error) {
            this.sendErrorResponse(sendResponse, error.message);
        }
    }

    handleSidePanelOpened(message, sender, sendResponse) {
        try {
            this.log("收到侧边栏打开消息");
            
            // 通知所有模块侧边栏已打开
            this.notifyModules('sidePanelOpened', message);
            
            this.sendSuccessResponse(sendResponse, { message: '侧边栏打开处理完成' });
        } catch (error) {
            this.sendErrorResponse(sendResponse, error.message);
        }
    }

    handleReportError(message, sender, sendResponse) {
        try {
            const { error, context } = message;
            
            this.error("前端错误报告:", {
                error: error,
                context: context,
                sender: sender,
                timestamp: Date.now()
            });
            
            // 可以在这里添加错误收集逻辑
            
            this.sendSuccessResponse(sendResponse, { reported: true });
        } catch (error) {
            this.sendErrorResponse(sendResponse, error.message);
        }
    }

    // 通知所有模块
    notifyModules(eventType, data) {
        if (!this.dependencies.moduleContainer) return;
        
        const modules = this.dependencies.moduleContainer.getAllModules();
        modules.forEach(module => {
            if (typeof module.onEvent === 'function') {
                try {
                    module.onEvent(eventType, data);
                } catch (error) {
                    this.error(`通知模块 ${module.name} 事件失败:`, error);
                }
            }
        });
    }

    // 启动消息队列处理器
    startMessageQueueProcessor() {
        this.createTimer(() => {
            this.processMessageQueue();
        }, 100, false); // 每100ms处理一次队列
    }

    // 处理消息队列
    processMessageQueue() {
        if (this.isProcessingQueue || this.messageQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;
        
        try {
            const batch = this.messageQueue.splice(0, 10); // 每次处理10条消息
            
            batch.forEach(({ message, sender, sendResponse, timestamp }) => {
                // 检查消息是否过期
                if (Date.now() - timestamp > this.responseTimeout) {
                    this.sendErrorResponse(sendResponse, '消息处理超时');
                    return;
                }
                
                this.handleMessage(message, sender, sendResponse);
            });
            
        } catch (error) {
            this.error("处理消息队列失败:", error);
        } finally {
            this.isProcessingQueue = false;
        }
    }

    // 启动消息统计日志
    startMessageStatsLogger() {
        this.createTimer(() => {
            this.logMessageStats();
        }, 60000, false); // 每分钟记录一次统计
    }

    // 记录消息统计
    logMessageStats() {
        if (this.messageStats.total > 0) {
            this.log("消息统计:", {
                total: this.messageStats.total,
                success: this.messageStats.success,
                error: this.messageStats.error,
                successRate: `${((this.messageStats.success / this.messageStats.total) * 100).toFixed(1)}%`,
                topActions: Array.from(this.messageStats.byAction.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 5)
            });
        }
    }

    // 获取内存使用情况
    getMemoryUsage() {
        try {
            if (performance.memory) {
                return {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                };
            }
        } catch (error) {
            // 忽略内存信息获取错误
        }
        return null;
    }

    // 获取状态
    getStatus() {
        return {
            ...super.getStatus(),
            registeredRoutes: this.messageRoutes.size,
            messageStats: this.messageStats,
            queueSize: this.messageQueue.length,
            isProcessingQueue: this.isProcessingQueue
        };
    }

    // 公共API
    broadcast(message) {
        // 广播消息到所有连接的前端
        this.sendMessageToUI(message);
    }

    getMessageStats() {
        return { ...this.messageStats };
    }

    clearMessageStats() {
        this.messageStats = {
            total: 0,
            success: 0,
            error: 0,
            byAction: new Map()
        };
    }

    getRegisteredRoutes() {
        return Array.from(this.messageRoutes.keys());
    }
}
