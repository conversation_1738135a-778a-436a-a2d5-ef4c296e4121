{"name": "shendengreader", "version": "0.52.0", "description": "AI-powered browser reading enhancement extension with TTS and continuous reading features", "scripts": {"lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "check:console": "grep -r \"console\\.log\" src/ || echo 'No debug logs found'", "check:unused": "echo 'Run ESLint to check for unused variables'", "validate": "npm run lint && npm run check:console", "build:clean": "node scripts/clean-build.js", "test": "echo 'Tests not implemented yet'"}, "keywords": ["browser-extension", "tts", "text-to-speech", "ai", "reader", "accessibility", "manifest-v3", "chrome-extension", "edge-extension"], "author": {"name": "神灯AI团队", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/shendengreader/extension", "repository": {"type": "git", "url": "https://github.com/shendengreader/extension.git"}, "bugs": {"url": "https://github.com/shendengreader/extension/issues"}, "devDependencies": {"eslint": "^8.0.0"}, "dependencies": {}, "engines": {"node": ">=14.0.0"}}