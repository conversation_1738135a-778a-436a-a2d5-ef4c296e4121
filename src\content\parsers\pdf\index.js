(function() {
    const PdfScanner = window.PdfScanner;
    const HtmlParser = window.HtmlParser;
    const PdfWrapper = window.PdfWrapper;

    class PdfParser {
        constructor() {
            this.scanner = new PdfScanner();
            this.isProcessing = false;
        }

        async parse(pdfUrl, options = {}) {
            if (this.isProcessing) {
                throw new Error('PDF解析器正忙');
            }

            this.isProcessing = true;
            try {
                // 初始化扫描效果
                this.scanner.start(1, 1);
                
                // 加载PDF文档
                const pdf = await PdfWrapper.loadDocument(pdfUrl);

                // 处理每一页
                let fullText = '';
                for (let i = 1; i <= pdf.numPages; i++) {
                    try {
                        const pageText = await PdfWrapper.getPageText(pdf, i);
                        fullText += pageText + '\n\n';
                    } catch (error) {
                        console.error(`处理第 ${i} 页时出错:`, error);
                    }
                }

                return {
                    title: pdf.info.Title || 'PDF文档',
                    textContent: fullText,
                    length: fullText.length,
                excerpt: fullText.substring(0, 200) + '...'
            };

        } finally {
            this.isProcessing = false;
            setTimeout(() => this.scanner.stop(), 1000);
        }
    }

    async processPage(pdf, pageNum) {
        const page = await pdf.getPage(pageNum);
        
        // 1. 尝试提取文本
        const textContent = await page.getTextContent();
        if (textContent.items.length > 0) {
            return textContent.items.map(item => item.str).join(' ');
        }
        
        // 2. 如果没有文本层，使用OCR
        console.log(`第 ${pageNum} 页未检测到文本层，使用OCR识别...`);
        const canvas = await page.renderToCanvas();
        return await this.ocr.recognize(canvas);
    }
}

    }

    // 导出单例
    window.PdfParser = new PdfParser();
})();
