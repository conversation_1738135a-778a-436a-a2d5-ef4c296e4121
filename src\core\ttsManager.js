// TTS Manager for 神灯 AI 朗读增强助手

// Manages different TTS engines and playback state

let currentUtterance = null;
let isSpeaking = false;
let speechQueue = []; // Array to hold text chunks for continuous reading
let currentRate = 1.0;
let currentVoice = null; // Store voice name or URI

// --- Browser TTS API --- (Default/Fallback)

function speakBrowserTTS(text) {
    return new Promise((resolve, reject) => {
        if (!text) {
            return reject("No text provided to speak.");
        }
        console.log(`Speaking (Browser TTS): "${text.substring(0, 50)}..."`);
        chrome.tts.speak(text, {
            voiceName: currentVoice, // Use selected voice if available
            rate: currentRate,
            onEvent: function(event) {
                if (event.type === 'end') {
                    console.log('Browser TTS finished chunk.');
                    isSpeaking = false;
                    resolve();
                } else if (event.type === 'error') {
                    console.error('Browser TTS Error:', event.errorMessage);
                    isSpeaking = false;
                    reject(event.errorMessage);
                } else if (event.type === 'start') {
                    isSpeaking = true;
                     console.log('Browser TTS started chunk.');
                }
                 // Other events: interrupted, paused, resumed, word, sentence
            }
        }, (utteranceId) => {
             if (chrome.runtime.lastError) {
                console.error("chrome.tts.speak failed:", chrome.runtime.lastError.message);
                isSpeaking = false;
                reject(chrome.runtime.lastError.message);
            } else {
                // Utterance started successfully (doesn't mean it finished)
                 currentUtterance = utteranceId; // Not very useful in manifest v3? state management needed
            }
        });
    });
}

function stopBrowserTTS() {
    console.log("Stopping Browser TTS");
    chrome.tts.stop();
    isSpeaking = false;
    speechQueue = []; // Clear the queue on stop
}

function pauseBrowserTTS() {
    console.log("Pausing Browser TTS (Note: True pause might not be supported, effectively stops)");
     // Chrome TTS API doesn't have a reliable pause/resume across all platforms/voices
     // Stopping is often the only reliable way.
    chrome.tts.stop(); 
    isSpeaking = false; 
    // TODO: Need to manage the queue and remaining text for a proper resume
}

function resumeBrowserTTS() {
     console.log("Resuming Browser TTS (Note: May restart from beginning of last chunk)");
     // TODO: Requires re-queuing the remaining text
     // playNextChunk(); // Or similar logic
}

// --- Generic TTS Control Interface --- 

export async function getAvailableVoices() {
    return new Promise((resolve) => {
        chrome.tts.getVoices(voices => {
            resolve(voices || []);
        });
    });
}

export function setRate(rate) {
    currentRate = parseFloat(rate) || 1.0;
    console.log("TTS rate set to:", currentRate);
    // If speaking, might need to stop and restart with new rate
    if (isSpeaking) {
        // TODO: Handle rate change during playback (might require stopping and restarting)
    }
}

export function setVoice(voiceIdentifier) {
    // voiceIdentifier could be voiceName for browser TTS, or an object for cloud TTS
    currentVoice = voiceIdentifier;
    console.log("TTS voice set to:", currentVoice);
     // If speaking, might need to stop and restart with new voice
     if (isSpeaking) {
        // TODO: Handle voice change during playback (might require stopping and restarting)
    }
}

/**
 * Adds text to the speech queue and starts playing if not already speaking.
 * @param {string | string[]} text Can be a single string or an array of text chunks.
 */
export function speak(text) {
    if (Array.isArray(text)) {
        speechQueue = speechQueue.concat(text);
    } else if (typeof text === 'string') {
        // TODO: Split long text into manageable chunks here?
        speechQueue.push(text);
    } else {
        console.error("Invalid text format provided to speak():", text);
        return;
    }
    
    if (!isSpeaking) {
        playNextChunk();
    }
}

async function playNextChunk() {
    if (speechQueue.length > 0) {
        const chunk = speechQueue.shift();
        try {
            // TODO: Select appropriate TTS engine based on settings (browser, cloud, local)
            await speakBrowserTTS(chunk);
            // Successfully finished chunk, play the next one if available
            if (speechQueue.length > 0) {
                 playNextChunk(); // Recursive call for next chunk
            } else {
                 console.log("Speech queue finished.");
                 // Optionally notify background script that reading is complete
                 // chrome.runtime.sendMessage({ action: "readingFinished" });
            }
        } catch (error) {
            console.error("Error speaking chunk:", error);
            // Decide how to handle errors: skip chunk, stop all, etc.
            isSpeaking = false;
             // Maybe try the next chunk?
             // playNextChunk(); 
        }
    } else {
        isSpeaking = false; // Ensure state is correct if queue is empty
         console.log("Attempted to play next chunk, but queue is empty.");
    }
}

export function pause() {
    // TODO: Implement pause logic based on the active TTS engine
    pauseBrowserTTS();
}

export function resume() {
    // TODO: Implement resume logic
    resumeBrowserTTS();
     if (!isSpeaking && speechQueue.length > 0) {
        // If paused state somehow left queue non-empty but not speaking
        playNextChunk();
    }
}

export function stop() {
    // TODO: Implement stop logic for all engines
    stopBrowserTTS();
}

console.log("TTS Manager initialized.");

// Note: This is a basic implementation focusing on Browser TTS.
// Integrating Cloud/Local TTS would require:
// 1. API clients/wrappers.
// 2. Secure handling of API keys (options page, storage).
// 3. Logic to choose the active engine.
// 4. Unified handling of playback states (speaking, paused, stopped) across engines.
// 5. Potentially handling audio streaming for better performance.
