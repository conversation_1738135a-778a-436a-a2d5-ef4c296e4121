// 性能监控器 - 监控和优化应用性能
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.timers = new Map();
        this.observers = new Map();
        this.isEnabled = false;
        this.maxMetrics = 1000; // 最大保存的指标数量
    }

    // 启用性能监控
    enable() {
        this.isEnabled = true;
        this.setupObservers();
        console.log('性能监控已启用');
    }

    // 禁用性能监控
    disable() {
        this.isEnabled = false;
        this.cleanup();
        console.log('性能监控已禁用');
    }

    // 设置观察器
    setupObservers() {
        // 监控DOM变化
        if (typeof window !== 'undefined' && window.MutationObserver) {
            const domObserver = new MutationObserver((mutations) => {
                this.recordMetric('dom.mutations', mutations.length);
            });

            domObserver.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true
            });

            this.observers.set('dom', domObserver);
        }

        // 监控内存使用（如果支持）
        if (typeof performance !== 'undefined' && performance.memory) {
            setInterval(() => {
                this.recordMetric('memory.used', performance.memory.usedJSHeapSize);
                this.recordMetric('memory.total', performance.memory.totalJSHeapSize);
                this.recordMetric('memory.limit', performance.memory.jsHeapSizeLimit);
            }, 5000);
        }
    }

    // 开始计时
    startTimer(name) {
        if (!this.isEnabled) return;
        
        this.timers.set(name, {
            start: performance.now(),
            marks: []
        });
    }

    // 添加计时标记
    mark(timerName, markName) {
        if (!this.isEnabled) return;
        
        const timer = this.timers.get(timerName);
        if (timer) {
            timer.marks.push({
                name: markName,
                time: performance.now() - timer.start
            });
        }
    }

    // 结束计时
    endTimer(name) {
        if (!this.isEnabled) return;
        
        const timer = this.timers.get(name);
        if (timer) {
            const duration = performance.now() - timer.start;
            this.recordMetric(`timer.${name}`, duration);
            this.timers.delete(name);
            return { duration, marks: timer.marks };
        }
        return null;
    }

    // 记录指标
    recordMetric(name, value, tags = {}) {
        if (!this.isEnabled) return;
        
        const timestamp = Date.now();
        const metric = {
            name,
            value,
            timestamp,
            tags
        };

        if (!this.metrics.has(name)) {
            this.metrics.set(name, []);
        }

        const metricArray = this.metrics.get(name);
        metricArray.push(metric);

        // 限制指标数量，删除旧的指标
        if (metricArray.length > this.maxMetrics) {
            metricArray.shift();
        }
    }

    // 监控函数执行性能
    monitor(name, func) {
        if (!this.isEnabled) {
            return func;
        }

        return (...args) => {
            this.startTimer(name);
            
            try {
                const result = func.apply(this, args);
                
                // 处理异步函数
                if (result && typeof result.then === 'function') {
                    return result
                        .then((value) => {
                            this.endTimer(name);
                            return value;
                        })
                        .catch((error) => {
                            this.endTimer(name);
                            this.recordMetric(`error.${name}`, 1);
                            throw error;
                        });
                } else {
                    this.endTimer(name);
                    return result;
                }
            } catch (error) {
                this.endTimer(name);
                this.recordMetric(`error.${name}`, 1);
                throw error;
            }
        };
    }

    // 监控DOM操作性能
    monitorDOMOperation(name, operation) {
        if (!this.isEnabled) {
            return operation();
        }

        this.startTimer(`dom.${name}`);
        const result = operation();
        this.endTimer(`dom.${name}`);
        
        return result;
    }

    // 获取指标统计
    getMetricStats(name) {
        const metrics = this.metrics.get(name);
        if (!metrics || metrics.length === 0) {
            return null;
        }

        const values = metrics.map(m => m.value);
        const sum = values.reduce((a, b) => a + b, 0);
        const avg = sum / values.length;
        const min = Math.min(...values);
        const max = Math.max(...values);

        return {
            count: values.length,
            sum,
            avg,
            min,
            max,
            latest: values[values.length - 1]
        };
    }

    // 获取所有指标
    getAllMetrics() {
        const result = {};
        this.metrics.forEach((metrics, name) => {
            result[name] = this.getMetricStats(name);
        });
        return result;
    }

    // 检测性能问题
    detectPerformanceIssues() {
        const issues = [];

        // 检查内存使用
        const memoryStats = this.getMetricStats('memory.used');
        if (memoryStats && memoryStats.latest > 50 * 1024 * 1024) { // 50MB
            issues.push({
                type: 'memory',
                severity: 'warning',
                message: `内存使用过高: ${(memoryStats.latest / 1024 / 1024).toFixed(2)}MB`
            });
        }

        // 检查DOM变化频率
        const domStats = this.getMetricStats('dom.mutations');
        if (domStats && domStats.avg > 100) {
            issues.push({
                type: 'dom',
                severity: 'warning',
                message: `DOM变化过于频繁: 平均${domStats.avg.toFixed(2)}次/监控周期`
            });
        }

        // 检查慢操作
        this.metrics.forEach((metrics, name) => {
            if (name.startsWith('timer.')) {
                const stats = this.getMetricStats(name);
                if (stats && stats.avg > 1000) { // 超过1秒
                    issues.push({
                        type: 'slow_operation',
                        severity: 'warning',
                        message: `操作${name}执行缓慢: 平均${stats.avg.toFixed(2)}ms`
                    });
                }
            }
        });

        return issues;
    }

    // 生成性能报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            metrics: this.getAllMetrics(),
            issues: this.detectPerformanceIssues(),
            summary: {
                totalMetrics: this.metrics.size,
                activeTimers: this.timers.size,
                memoryUsage: this.getMetricStats('memory.used')?.latest || 0
            }
        };

        return report;
    }

    // 清理资源
    cleanup() {
        this.observers.forEach((observer) => {
            observer.disconnect();
        });
        this.observers.clear();
        this.timers.clear();
    }

    // 重置所有指标
    reset() {
        this.metrics.clear();
        this.timers.clear();
    }

    // 导出指标数据
    exportMetrics() {
        const data = {};
        this.metrics.forEach((metrics, name) => {
            data[name] = metrics;
        });
        return JSON.stringify(data, null, 2);
    }

    // 获取性能建议
    getPerformanceRecommendations() {
        const recommendations = [];
        const issues = this.detectPerformanceIssues();

        issues.forEach(issue => {
            switch (issue.type) {
                case 'memory':
                    recommendations.push('考虑清理未使用的对象和事件监听器');
                    break;
                case 'dom':
                    recommendations.push('减少DOM操作频率，考虑使用批量更新');
                    break;
                case 'slow_operation':
                    recommendations.push('优化慢操作，考虑使用异步处理或分片执行');
                    break;
            }
        });

        return recommendations;
    }
}

// 创建全局性能监控器实例
export const performanceMonitor = new PerformanceMonitor();

// 便捷的监控函数
export const startTimer = (name) => performanceMonitor.startTimer(name);
export const endTimer = (name) => performanceMonitor.endTimer(name);
export const recordMetric = (name, value, tags) => performanceMonitor.recordMetric(name, value, tags);
export const monitor = (name, func) => performanceMonitor.monitor(name, func);

// 在开发环境中自动启用
if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
    const manifest = chrome.runtime.getManifest();
    if (manifest.version.includes('dev') || manifest.version.includes('beta')) {
        performanceMonitor.enable();
    }
}

console.log("性能监控器已初始化");
