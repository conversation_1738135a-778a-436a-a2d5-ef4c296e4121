<!DOCTYPE html>
<html>
<head>
    <title>神灯</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: sans-serif;
            width: 200px; /* 增加宽度以适应更多按钮 */
            padding: 10px;
            text-align: center;
        }
        button {
            width: 100%;
            padding: 8px;
            margin-top: 8px;
            cursor: pointer;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        #open-options {
            background-color: #2196F3;
        }
        #open-options:hover {
            background-color: #0b7dda;
        }
    </style>
</head>
<body>
    <h4>神灯AI·灵阅</h4>
    <button id="open-sidepanel">打开侧边栏</button>
    <button id="open-options">设置</button>
    <!-- Optional: Maybe add a quick play/pause? -->
    <!-- <button id="quick-toggle">播放/暂停</button> -->
    <script src="index.js"></script>
</body>
</html>
