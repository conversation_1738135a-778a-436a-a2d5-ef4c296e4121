// 使用全局变量替代import (库通过manifest.json加载)
const { createWorker } = Tesseract;
const { PDFDocument } = PDFLib;

// Tesseract OCR Worker 实例
let ocrWorker = null;

// 初始化OCR Worker
async function initOCRWorker() {
  if (!ocrWorker) {
    console.log('初始化Tesseract OCR Worker');
    ocrWorker = createWorker({
      logger: m => console.log('OCR进度:', m),
    });
    await ocrWorker.load();
    await ocrWorker.loadLanguage('chi_sim+eng');
    await ocrWorker.initialize('chi_sim+eng');
  }
  return ocrWorker;
}

// 销毁OCR Worker
async function destroyOCRWorker() {
  if (ocrWorker) {
    console.log('销毁Tesseract OCR Worker');
    await ocrWorker.terminate();
    ocrWorker = null;
  }
}

// 将PDF页面渲染为图像
async function renderPdfPage(pdfBytes, pageNumber) {
  try {
    const pdfDoc = await PDFDocument.load(pdfBytes);
    const pages = pdfDoc.getPages();
    if (pageNumber < 1 || pageNumber > pages.length) {
      throw new Error(`无效的页码: ${pageNumber}`);
    }

    // 创建仅包含目标页面的临时PDF
    const tempPdfDoc = await PDFDocument.create();
    const [copiedPage] = await tempPdfDoc.copyPages(pdfDoc, [pageNumber - 1]);
    tempPdfDoc.addPage(copiedPage);

    // 将临时PDF转换为数据URL
    const pdfBytes = await tempPdfDoc.saveAsBase64({
      dataUri: true
    });

    // 使用pdf.js渲染页面为图像
    const pdfjsLib = window['pdfjs-dist/build/pdf'];
    pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('src/lib/pdf.worker.min.js');

    const loadingTask = pdfjsLib.getDocument({ data: atob(pdfBytes.split(',')[1]) });
    const pdf = await loadingTask.promise;
    const page = await pdf.getPage(1);
    const viewport = page.getViewport({ scale: 2.0 });

    // 创建Canvas元素
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.height = viewport.height;
    canvas.width = viewport.width;

    // 渲染PDF页面到Canvas
    const renderContext = {
      canvasContext: context,
      viewport: viewport
    };
    await page.render(renderContext).promise;

    return canvas.toDataURL('image/png');
  } catch (error) {
    console.error('PDF页面渲染失败:', error);
    throw error;
  }
}

// 识别图像中的文本
async function recognizeImageText(imageDataUrl) {
  try {
    const worker = await initOCRWorker();
    const { data: { text } } = await worker.recognize(imageDataUrl);
    return text;
  } catch (error) {
    console.error('图像文本识别失败:', error);
    throw error;
  }
}

// 解析PDF文件并提取文本
async function parsePdfContent(pdfUrl) {
  try {
    console.log(`开始解析PDF文件: ${pdfUrl}`);
    let pdfBytes;

    // 处理本地文件和远程文件
    if (pdfUrl.startsWith('file://')) {
      // 本地文件需要通过扩展API读取
      const response = await fetch(pdfUrl);
      pdfBytes = await response.arrayBuffer();
    } else {
      // 远程文件直接获取
      const response = await fetch(pdfUrl);
      pdfBytes = await response.arrayBuffer();
    }

    // 加载PDF文档
    const pdfDoc = await PDFDocument.load(pdfBytes);
    const pageCount = pdfDoc.getPages().length;
    console.log(`PDF文件加载完成，共${pageCount}页`);

    let fullText = '';

    // 逐页处理
    for (let i = 1; i <= pageCount; i++) {
      console.log(`处理第${i}/${pageCount}页`);
      const imageDataUrl = await renderPdfPage(pdfBytes, i);
      const pageText = await recognizeImageText(imageDataUrl);
      fullText += `\n\n=== 第${i}页 ===\n${pageText}`;
    }

    // 销毁OCR Worker
    await destroyOCRWorker();

    return {
      title: pdfUrl.split('/').pop().replace('.pdf', ''),
      url: pdfUrl,
      textContent: fullText,
      pageCount: pageCount
    };
  } catch (error) {
    console.error('PDF内容解析失败:', error);
    throw error;
  }
}

// 检查URL是否为PDF文件
function isPdfUrl(url) {
  return url.toLowerCase().endsWith('.pdf');
}

// 暴露函数到全局作用域，供injector调用
window.parsePdfContent = parsePdfContent;
window.isPdfUrl = isPdfUrl;

// 清理函数
window.addEventListener('beforeunload', async () => {
  await destroyOCRWorker();
});

// 消息监听器 - 处理来自background的PDF解析请求
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'parsePdfContent') {
    console.log('🔍 收到PDF解析请求:', message.url);
    parsePdfContent(message.url)
      .then(result => {
        sendResponse({ success: true, data: result });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // 保持消息通道开放以支持异步响应
  }
});