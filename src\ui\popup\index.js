// Popup UI Logic - Primarily to open the side panel

console.log("Popup script loaded.");

document.getElementById('open-sidepanel').addEventListener('click', () => {
    console.log("Open Side Panel button clicked.");
    // Find the current window and open the side panel
    chrome.windows.getCurrent((window) => {
        if (chrome.runtime.lastError) {
            console.error("Error getting current window:", chrome.runtime.lastError);
            return;
        }
        if (!window) {
             console.error("Could not get current window.");
             return;
        }
        console.log("Requesting side panel open for window ID:", window.id);
        chrome.sidePanel.open({ windowId: window.id }, () => {
            if (chrome.runtime.lastError) {
                 console.error("Error opening side panel:", chrome.runtime.lastError);
                 // Fallback or inform user?
            } else {
                 console.log("Side panel opened successfully or was already open.");
                 // Close the popup once the side panel is open?
                 // window.close(); // Might be too abrupt
            }
           
        });
    });
});

// Open options page when settings button is clicked
document.getElementById('open-options').addEventListener('click', () => {
    console.log("Open Options button clicked.");
    chrome.runtime.openOptionsPage(() => {
        if (chrome.runtime.lastError) {
            console.error("Error opening options page:", chrome.runtime.lastError);
            // Fallback: Open options page in a new tab
            chrome.tabs.create({ url: chrome.runtime.getURL('src/ui/options/index.html') });
        }
    });
});

// Optional: Quick toggle logic if added
// document.getElementById('quick-toggle').addEventListener('click', () => {
//     // Send message to background to toggle play/pause
//     chrome.runtime.sendMessage({ action: 'togglePlayback' }, (response) => {
//        console.log("Toggle response:", response);
//        // Maybe update button text based on response?
//     });
// });
