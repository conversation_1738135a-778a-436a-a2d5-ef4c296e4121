// State Manager for 神灯 AI 朗读增强助手

// Manages the extension's state, potentially syncing with storage

import { getStorage, setStorage } from '../utils/storage.js';

// Default state
const defaultState = {
    readingState: 'idle', // idle, reading, paused, stopped, error
    currentSpeed: 1.0,
    currentVoice: null, // Voice name/URI
    continuousReading: true,
    readingProgress: {
        // url: { currentSentence: 0, totalSentences: 100, timestamp: ... }
    },
    currentUrl: null,
    currentTextChunks: [],
    currentChunkIndex: 0,
    error: null
};

// In-memory state cache
let currentState = { ...defaultState };
let stateLoaded = false;
let listeners = [];

/**
 * Initializes the state manager, loading state from storage.
 */
export async function initState() {
    if (stateLoaded) return currentState;
    try {
        const storedState = await getStorage(Object.keys(defaultState));
        // Merge stored state with defaults, giving precedence to stored values
        // Only load keys that are part of our defined state
        for (const key of Object.keys(defaultState)) {
            if (storedState[key] !== undefined) {
                currentState[key] = storedState[key];
            }
        }
        stateLoaded = true;
        console.log("State loaded from storage:", currentState);
        notifyListeners();
    } catch (error) {
        console.error("Failed to load state from storage:", error);
        // Use default state in case of error
        currentState = { ...defaultState };
    } finally {
         // Ensure defaults are set if they weren't in storage
        for (const key in defaultState) {
            if (currentState[key] === undefined) {
                currentState[key] = defaultState[key];
            }
        }
    }
    return currentState;
}

/**
 * Gets the current state.
 * Loads from storage on first call if not already loaded.
 * @returns {Promise<object>} The current state object.
 */
export async function getState() {
    if (!stateLoaded) {
        return await initState();
    }
    return { ...currentState }; // Return a copy to prevent direct mutation
}

/**
 * Updates the state with new values and persists to storage.
 * @param {object} newStateChanges An object containing the state keys and values to update.
 */
export async function updateState(newStateChanges) {
    const oldState = { ...currentState }; // Copy for comparison
    let hasChanged = false;

    for (const key in newStateChanges) {
        if (newStateChanges.hasOwnProperty(key) && defaultState.hasOwnProperty(key)) {
            if (currentState[key] !== newStateChanges[key]) {
                 currentState[key] = newStateChanges[key];
                 hasChanged = true;
            }
        } else {
            console.warn(`Attempted to update non-existent state key: ${key}`);
        }
    }

    if (hasChanged) {
        console.log("State updated:", newStateChanges);
        notifyListeners(oldState);
        try {
            // Persist only the changed keys relevant to storage
            const changesToStore = {};
            const keysToPersist = ['readingState', 'currentSpeed', 'currentVoice', 'continuousReading', 'readingProgress']; 
            for (const key of keysToPersist) {
                 if (newStateChanges.hasOwnProperty(key)) {
                     changesToStore[key] = currentState[key];
                 }
            }
            if (Object.keys(changesToStore).length > 0) {
                await setStorage(changesToStore);
                console.log("State changes persisted to storage:", changesToStore);
            }
           
        } catch (error) {
            console.error("Failed to persist state changes to storage:", error);
            // TODO: Potential rollback or error handling?
        }
    } 
}

/**
 * Subscribes a listener function to state changes.
 * @param {function} listener The function to call when state changes. 
 *                            It receives (newState, oldState) as arguments.
 * @returns {function} An unsubscribe function.
 */
export function subscribe(listener) {
    listeners.push(listener);
    return () => {
        listeners = listeners.filter(l => l !== listener);
    };
}

/**
 * Notifies all subscribed listeners about a state change.
 * @param {object} oldState The state before the update.
 */
function notifyListeners(oldState = {}) {
    const newState = { ...currentState };
    listeners.forEach(listener => {
        try {
             listener(newState, oldState);
        } catch (error) { 
             console.error("Error in state listener:", error);
        }
       
    });
}

// Initialize state when the module loads (e.g., in the service worker)
// initState(); // Consider calling this explicitly in the background script entry point.

console.log("State Manager initialized.");

// Note: This state manager uses local storage.
// For sync storage (cross-device), use chrome.storage.sync instead,
// keeping in mind its stricter quotas and potential latency.
