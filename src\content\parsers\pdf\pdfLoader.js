const PDFJS = require('pdfjs-dist');
const { pdfjsWorker } = require('pdfjs-dist/legacy/build/pdf.worker.entry');

class PdfLoader {
    constructor() {
        this.pdfjsLib = null;
    }

    async load(pdfUrl, callbacks = {}) {
        try {
            // 动态加载PDF.js
            const pdfjsLib = require('pdfjs-dist');
            pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('src/lib/pdf.worker.min.js');

            // 加载PDF文档
            const loadingTask = pdfjsLib.getDocument(pdfUrl);
            const pdf = await loadingTask.promise;
        
            if (!pdf) {
                throw new Error('无法加载PDF文档');
        // 添加页面加载回调
        const originalGetPage = pdf.getPage;
        pdf.getPage = async function(pageNumber) {
            const page = await originalGetPage.call(this, pageNumber);
            
            // 触发页面加载回调
            if (callbacks.onPageLoad) {
                const viewport = page.getViewport({ scale: 1.0 });
                callbacks.onPageLoad(
                    pageNumber,
                    this.numPages,
                    {
                        left: 0,
                        top: 0,
                        width: viewport.width,
                        height: viewport.height
                    }
                );
            }
            
            // 添加渲染到Canvas的方法
            page.renderToCanvas = async (options = {}) => {
                const viewport = page.getViewport(options.scale || 2.0);
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                await page.render({
                    canvasContext: context,
                    viewport: viewport
                }).promise;

                return canvas;
            };
            
            return page;
        };

        return pdf;
    }
}
