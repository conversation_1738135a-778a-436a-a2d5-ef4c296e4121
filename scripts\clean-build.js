#!/usr/bin/env node

/**
 * 代码清理脚本 - 为生产环境准备代码
 * 用于清理调试日志、未使用变量等不符合商店规范的内容
 */

const fs = require('fs');
const path = require('path');

class CodeCleaner {
    constructor() {
        this.srcDir = path.join(__dirname, '..', 'src');
        this.issues = [];
        this.fixed = [];
    }

    // 主清理函数
    async clean() {
        console.log('🧹 开始代码清理...');
        
        try {
            // 1. 扫描所有JS文件
            const jsFiles = this.findJSFiles(this.srcDir);
            console.log(`📁 找到 ${jsFiles.length} 个JS文件`);

            // 2. 清理每个文件
            for (const file of jsFiles) {
                await this.cleanFile(file);
            }

            // 3. 生成报告
            this.generateReport();

        } catch (error) {
            console.error('❌ 清理过程中出错:', error);
            process.exit(1);
        }
    }

    // 查找所有JS文件
    findJSFiles(dir) {
        const files = [];
        
        const scan = (currentDir) => {
            const items = fs.readdirSync(currentDir);
            
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    // 跳过node_modules和lib目录
                    if (!['node_modules', 'lib'].includes(item)) {
                        scan(fullPath);
                    }
                } else if (item.endsWith('.js')) {
                    files.push(fullPath);
                }
            }
        };
        
        scan(dir);
        return files;
    }

    // 清理单个文件
    async cleanFile(filePath) {
        const relativePath = path.relative(process.cwd(), filePath);
        console.log(`🔍 检查文件: ${relativePath}`);
        
        let content = fs.readFileSync(filePath, 'utf8');
        const originalContent = content;
        let fileIssues = [];
        let fileFixed = [];

        // 1. 检查console.log
        const consoleLogMatches = content.match(/console\.log\([^)]*\)/g);
        if (consoleLogMatches) {
            fileIssues.push(`发现 ${consoleLogMatches.length} 个 console.log`);
            
            // 替换为条件日志（如果有logger导入）
            if (content.includes('import') && content.includes('logger')) {
                content = content.replace(/console\.log\(/g, 'logger.debug(');
                fileFixed.push('将 console.log 替换为 logger.debug');
            } else {
                // 注释掉console.log
                content = content.replace(/(\s*)console\.log\([^)]*\);?/g, '$1// console.log removed for production');
                fileFixed.push('注释掉 console.log 语句');
            }
        }

        // 2. 检查console.info
        const consoleInfoMatches = content.match(/console\.info\([^)]*\)/g);
        if (consoleInfoMatches) {
            fileIssues.push(`发现 ${consoleInfoMatches.length} 个 console.info`);
            content = content.replace(/(\s*)console\.info\([^)]*\);?/g, '$1// console.info removed for production');
            fileFixed.push('注释掉 console.info 语句');
        }

        // 3. 检查eval使用
        if (content.includes('eval(')) {
            fileIssues.push('发现 eval() 使用 - CSP违规');
        }

        // 4. 检查未使用的变量（简单检查）
        const unusedVarPattern = /const\s+(\w+)\s*=.*?;[\s\S]*?(?!.*\b\1\b)/g;
        // 这是一个简化的检查，实际应该使用AST分析

        // 5. 保存修改后的文件
        if (content !== originalContent) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 已修复: ${relativePath}`);
        }

        // 记录问题和修复
        if (fileIssues.length > 0) {
            this.issues.push({ file: relativePath, issues: fileIssues });
        }
        if (fileFixed.length > 0) {
            this.fixed.push({ file: relativePath, fixes: fileFixed });
        }
    }

    // 生成清理报告
    generateReport() {
        console.log('\n📊 清理报告');
        console.log('='.repeat(50));
        
        if (this.issues.length === 0) {
            console.log('✅ 未发现需要修复的问题');
        } else {
            console.log(`⚠️ 发现问题的文件: ${this.issues.length} 个`);
            this.issues.forEach(({ file, issues }) => {
                console.log(`\n📁 ${file}:`);
                issues.forEach(issue => console.log(`  - ${issue}`));
            });
        }

        if (this.fixed.length > 0) {
            console.log(`\n🔧 已修复的文件: ${this.fixed.length} 个`);
            this.fixed.forEach(({ file, fixes }) => {
                console.log(`\n📁 ${file}:`);
                fixes.forEach(fix => console.log(`  ✅ ${fix}`));
            });
        }

        console.log('\n🎉 代码清理完成！');
        
        // 生成JSON报告
        const report = {
            timestamp: new Date().toISOString(),
            issues: this.issues,
            fixed: this.fixed,
            summary: {
                totalFiles: this.issues.length + this.fixed.length,
                issuesFound: this.issues.length,
                filesFixed: this.fixed.length
            }
        };
        
        fs.writeFileSync('clean-report.json', JSON.stringify(report, null, 2));
        console.log('📄 详细报告已保存到 clean-report.json');
    }
}

// 运行清理
if (require.main === module) {
    const cleaner = new CodeCleaner();
    cleaner.clean().catch(console.error);
}

module.exports = CodeCleaner;
