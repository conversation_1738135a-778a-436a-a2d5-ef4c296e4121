// PDF.js library for PDF rendering
// This file is dynamically loaded by the PDF parser
// It provides the core PDF.js functionality needed for PDF rendering and text extraction

// Initialize PDF.js with worker source
if (typeof window !== 'undefined' && window.pdfjsLib) {
    window.pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('src/lib/pdf.worker.min.js');
    window.pdfjsLib.PDFJS.workerSrc = chrome.runtime.getURL('src/lib/pdf.worker.min.js');
    window.pdfjsLib.PDFJS.cMapUrl = chrome.runtime.getURL('src/lib/cmaps/');
    window.pdfjsLib.PDFJS.cMapPacked = true;
    console.log('PDF.js初始化完成');
} else {
    console.error('PDF.js库未正确加载');
}
