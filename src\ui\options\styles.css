body {
    font-family: sans-serif;
    padding: 20px;
    margin: 0 auto; /* Center content */
    max-width: 600px;
    background-color: #f4f4f4;
}

.container {
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 25px;
}

section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

section:last-of-type {
     border-bottom: none;
     margin-bottom: 20px;
}

h2 {
    font-size: 1.1em;
    color: #555;
    margin-bottom: 15px;
}

.option {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
}

.option label {
    min-width: 180px; /* Adjust as needed for alignment */
    flex-shrink: 0; /* Prevent label shrinking */
    margin-right: 15px;
    color: #444;
    font-size: 0.95em;
}

.option input[type="range"] {
    flex-grow: 1;
    margin-right: 10px;
}

.option select,
.option input[type="password"],
.option input[type="text"] {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9em;
}

.option input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-left: 5px; /* Adjust spacing if needed */
}

.option span {
    min-width: 35px; /* For speed value, api status */
    text-align: right;
    font-weight: bold;
    color: #333;
}

/* Specific element styling */
#default-speed-value {
    font-size: 0.9em;
}

.api-key-config {
     /* Styles for API key section when visible */
     padding-left: 195px; /* Indent to align with other controls */
     margin-top: -10px; /* Reduce space above */
}

.api-key-config label {
    min-width: 0; /* Reset min-width */
    margin-right: 10px;
}

.api-status {
    font-size: 0.85em;
    margin-left: 10px;
}

#save-button {
    display: block; /* Center button */
    margin: 20px auto 10px auto;
    padding: 10px 25px;
    font-size: 1em;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

#save-button:hover:not(:disabled) {
    background-color: #45a049;
}

#save-button:disabled {
     background-color: #aaa;
     cursor: not-allowed;
}

#save-status {
    text-align: center;
    margin-top: 10px;
    font-size: 0.9em;
    min-height: 1.2em; /* Reserve space */
}

#version {
    font-weight: bold;
}
