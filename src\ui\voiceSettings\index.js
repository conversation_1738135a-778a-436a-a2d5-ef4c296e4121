/**
 * 语音设置页面 - 实现语音筛选、收藏和管理功能
 * 遵循设计文档中的"Experiential Ambient"设计理念
 */

// === 状态管理 ===
let availableVoices = []; // 所有可用语音列表
let favoriteVoices = []; // 收藏的语音列表
let defaultVoice = ''; // 当前默认语音
let currentFilters = {
    language: [], // 当前语言筛选
    dialect: []   // 当前方言筛选
};
let searchText = ''; // 当前搜索文本

// === DOM元素引用 ===
const closeBtn = document.getElementById('close-voice-settings-btn');
const saveBtn = document.getElementById('save-settings-btn');
const searchInput = document.getElementById('voice-search-input');
const voicesList = document.getElementById('voices-list');
const loadingMessage = document.getElementById('loading-message');
const filterTags = document.querySelectorAll('.filter-tag');
const voiceItemTemplate = document.getElementById('voice-item-template');
const emptyStateTemplate = document.getElementById('empty-state-template');

// === 初始化 ===
document.addEventListener('DOMContentLoaded', initializeVoiceSettings);

/**
 * 初始化语音设置页面
 */
function initializeVoiceSettings() {
    console.log('初始化语音设置页面');
    
    // 加载收藏的语音
    loadFavoriteVoices();
    
    // 加载默认语音
    loadDefaultVoice();
    
    // 加载筛选标签状态
    loadFilterState();
    
    // 加载所有可用语音
    fetchVoices();
    
    // 设置事件监听器
    setupEventListeners();
}

// === 数据加载功能 ===

/**
 * 从存储中加载收藏的语音
 */
function loadFavoriteVoices() {
    try {
        // 优先从chrome.storage.local加载
        chrome.storage.local.get('favoriteVoices', (result) => {
            if (result.favoriteVoices) {
                favoriteVoices = result.favoriteVoices;
                console.log('从chrome.storage加载收藏语音:', favoriteVoices);
                // 更新渲染
                if (availableVoices.length > 0) {
                    renderVoicesList();
                }
            } else {
                // 回退到localStorage
                const saved = localStorage.getItem('favoriteVoices');
                if (saved) {
                    favoriteVoices = JSON.parse(saved);
                    console.log('从localStorage加载收藏语音:', favoriteVoices);
                    
                    // 同步到chrome.storage.local
                    chrome.storage.local.set({ favoriteVoices });
                    
                    // 更新渲染
                    if (availableVoices.length > 0) {
                        renderVoicesList();
                    }
                }
            }
        });
    } catch (error) {
        console.error('加载收藏语音时出错:', error);
        favoriteVoices = [];
    }
}

/**
 * 保存收藏的语音到存储
 */
function saveFavoriteVoices() {
    try {
        // 保存到localStorage
        localStorage.setItem('favoriteVoices', JSON.stringify(favoriteVoices));
        
        // 同时保存到chrome.storage.local
        chrome.storage.local.set({ favoriteVoices }, () => {
            if (chrome.runtime.lastError) {
                console.error("保存收藏语音到chrome.storage失败:", chrome.runtime.lastError);
            } else {
                console.log("收藏语音已保存到storage:", favoriteVoices);
            }
        });
    } catch (error) {
        console.error('保存收藏语音时出错:', error);
    }
}

/**
 * 从存储中加载默认语音
 */
function loadDefaultVoice() {
    try {
        // 优先从chrome.storage.local加载
        chrome.storage.local.get('currentVoice', (result) => {
            if (result.currentVoice) {
                defaultVoice = result.currentVoice;
                console.log('从chrome.storage加载默认语音:', defaultVoice);
                // 更新渲染
                if (availableVoices.length > 0) {
                    renderVoicesList();
                }
            } else {
                // 回退到localStorage
                const savedVoice = localStorage.getItem('defaultVoice');
                if (savedVoice) {
                    defaultVoice = savedVoice;
                    console.log('从localStorage加载默认语音:', defaultVoice);
                    
                    // 同步到chrome.storage.local
                    chrome.storage.local.set({ currentVoice: defaultVoice });
                    
                    // 更新渲染
                    if (availableVoices.length > 0) {
                        renderVoicesList();
                    }
                }
            }
        });
    } catch (error) {
        console.error('加载默认语音时出错:', error);
        defaultVoice = '';
    }
}

/**
 * 保存默认语音到存储
 */
function saveDefaultVoice() {
    try {
        // 保存到localStorage
        localStorage.setItem('defaultVoice', defaultVoice);
        
        // 同时保存到chrome.storage.local（背景脚本中使用currentVoice作为键名）
        chrome.storage.local.set({ currentVoice: defaultVoice }, () => {
            if (chrome.runtime.lastError) {
                console.error("保存默认语音到chrome.storage失败:", chrome.runtime.lastError);
            } else {
                console.log("默认语音已保存到storage:", defaultVoice);
            }
        });
        
        // 通知背景脚本更新默认语音
        chrome.runtime.sendMessage({
            action: 'setVoice',
            payload: defaultVoice
        });
        
        console.log('已保存默认语音:', defaultVoice);
    } catch (error) {
        console.error('保存默认语音时出错:', error);
    }
}

/**
 * 从存储中加载筛选标签状态
 */
function loadFilterState() {
    try {
        // 优先从chrome.storage.local加载
        chrome.storage.local.get('voiceFilters', (result) => {
            if (result.voiceFilters) {
                currentFilters = result.voiceFilters;
                console.log('从chrome.storage加载筛选状态:', currentFilters);
                
                // 更新UI上的筛选标签状态
                updateFilterTagsUI();
            } else {
                // 回退到localStorage
                const saved = localStorage.getItem('voiceFilters');
                if (saved) {
                    currentFilters = JSON.parse(saved);
                    console.log('从localStorage加载筛选状态:', currentFilters);
                    
                    // 同步到chrome.storage.local
                    chrome.storage.local.set({ voiceFilters: currentFilters });
                    
                    // 更新UI上的筛选标签状态
                    updateFilterTagsUI();
                }
            }
        });
    } catch (error) {
        console.error('加载筛选状态时出错:', error);
        // 重置为默认状态
        currentFilters = {
            language: [],
            dialect: []
        };
    }
}

/**
 * 更新UI上的筛选标签状态
 */
function updateFilterTagsUI() {
    // 首先清除所有标签的激活状态
    filterTags.forEach(tag => {
        tag.classList.remove('active');
    });
    
    // 如果没有选中任何筛选，则激活"全部"标签
    if (currentFilters.language.length === 0 && currentFilters.dialect.length === 0) {
        document.querySelector('.filter-tag[data-filter="all"]').classList.add('active');
        return;
    }
    
    // 激活已选中的语言标签
    currentFilters.language.forEach(lang => {
        const langTag = document.querySelector(`.filter-tag[data-filter="${lang}"]`);
        if (langTag) langTag.classList.add('active');
    });
    
    // 激活已选中的方言标签
    currentFilters.dialect.forEach(dialect => {
        const dialectTag = document.querySelector(`.filter-tag[data-filter="${dialect}"]`);
        if (dialectTag) dialectTag.classList.add('active');
    });
}

/**
 * 保存筛选标签状态到存储
 */
function saveFilterState() {
    try {
        // 确保当前筛选状态是有效的对象
        if (!currentFilters || typeof currentFilters !== 'object') {
            console.warn('筛选状态无效，重置为默认状态');
            currentFilters = {
                language: [],
                dialect: []
            };
        }
        
        // 保存到localStorage
        localStorage.setItem('voiceFilters', JSON.stringify(currentFilters));
        
        // 同时保存到chrome.storage.local
        chrome.storage.local.set({ voiceFilters: currentFilters }, () => {
            if (chrome.runtime.lastError) {
                console.error("保存筛选状态到chrome.storage失败:", chrome.runtime.lastError);
            } else {
                console.log("筛选状态已保存到storage:", currentFilters);
            }
        });
    } catch (error) {
        console.error('保存筛选状态时出错:', error);
    }
}

/**
 * 获取所有可用语音
 */
function fetchVoices() {
    showLoadingMessage();
    
    // 使用chrome.tts API获取所有语音
    chrome.tts.getVoices((voices) => {
        console.log('已获取语音列表:', voices);
        availableVoices = voices || [];
        
        if (availableVoices.length === 0) {
            showNoVoicesMessage();
            return;
        }
        
        // 处理获取到的语音列表
        processVoices();
    });
}

/**
 * 处理获取到的语音列表
 */
function processVoices() {
    // 生成方言标签
    generateDialectTags();
    
    // 渲染语音列表
    renderVoicesList();
}

// === 渲染功能 ===

/**
 * 显示加载中的提示消息
 */
function showLoadingMessage() {
    voicesList.innerHTML = '';
    loadingMessage.style.display = 'block';
    loadingMessage.textContent = '加载中...';
    voicesList.appendChild(loadingMessage);
}

/**
 * 显示无可用语音的提示消息
 */
function showNoVoicesMessage() {
    voicesList.innerHTML = '';
    const emptyState = createEmptyState('未找到语音', '当前系统中没有可用的语音，请检查系统设置或使用第三方TTS引擎');
    voicesList.appendChild(emptyState);
}

/**
 * 创建空状态元素
 * @param {string} title - 空状态标题
 * @param {string} message - 空状态提示信息
 * @returns {HTMLElement} - 空状态DOM元素
 */
function createEmptyState(title, message) {
    const clone = emptyStateTemplate.content.cloneNode(true);
    const emptyState = clone.querySelector('.empty-state');
    const titleEl = emptyState.querySelector('.empty-state-title');
    const messageEl = emptyState.querySelector('.empty-state-message');
    
    titleEl.textContent = title;
    messageEl.textContent = message;
    
    return emptyState;
}

/**
 * 创建收藏语音为空时的状态
 * @returns {HTMLElement} - 空状态DOM元素
 */
function createEmptyFavoritesState() {
    return createEmptyState(
        '尚未添加收藏语音',
        '点击语音列表中的 ☆ 将语音添加到收藏，收藏的语音将显示在侧边栏中方便快速选择'
    );
}

/**
 * 创建搜索无结果时的状态
 * @returns {HTMLElement} - 空状态DOM元素
 */
function createNoResultsState() {
    return createEmptyState(
        '未找到匹配的语音',
        '• 尝试其他关键词\n• 减少筛选条件\n• 检查是否有拼写错误'
    );
}

/**
 * 根据可用语音生成方言标签
 */
function generateDialectTags() {
    // 获取所有可用方言
    const availableDialects = new Set();
    
    availableVoices.forEach(voice => {
        const dialect = detectDialect(voice);
        if (dialect) {
            availableDialects.add(dialect.code);
        }
    });
    
    // 清空方言标签容器
    const dialectTagsContainer = document.getElementById('dialect-tags-container');
    dialectTagsContainer.innerHTML = '';
    
    // 只有在有方言的情况下才显示方言标签
    if (availableDialects.size > 0) {
        // 添加方言标签
        availableDialects.forEach(dialectCode => {
            const dialectName = getDialectName(dialectCode);
            const dialectTag = document.createElement('button');
            dialectTag.className = 'filter-tag';
            dialectTag.dataset.filter = dialectCode;
            dialectTag.dataset.type = 'dialect';
            dialectTag.textContent = dialectName;
            
            // 如果方言在当前筛选中，则显示为选中状态
            if (currentFilters.dialect.includes(dialectCode)) {
                dialectTag.classList.add('active');
            }
            
            // 添加点击事件
            dialectTag.addEventListener('click', () => toggleDialectFilter(dialectCode));
            
            // 添加到容器
            dialectTagsContainer.appendChild(dialectTag);
        });
    } else {
        // 如果没有方言，隐藏方言标签容器
        dialectTagsContainer.style.display = 'none';
    }
}

// === 事件处理 ===

/**
 * 设置所有事件监听器
 */
function setupEventListeners() {
    // 关闭按钮
    closeBtn.addEventListener('click', closeVoiceSettings);
    
    // 保存按钮
    saveBtn.addEventListener('click', saveAndClose);
    
    // 搜索框
    searchInput.addEventListener('input', handleSearchInput);
    
    // 筛选标签
    filterTags.forEach(tag => {
        tag.addEventListener('click', handleFilterTagClick);
    });
    
    // 监听来自侧边栏的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('接收到消息:', message);
        if (message.action === 'closeVoiceSettings') {
            console.log('收到关闭语音设置页面的消息');
            closeVoiceSettings();
        }
        // 返回true表示异步响应
        return true;
    });
}

/**
 * 处理搜索输入
 */
function handleSearchInput() {
    searchText = searchInput.value.toLowerCase().trim();
    renderVoicesList();
}

/**
 * 处理筛选标签点击
 * @param {Event} event - 点击事件
 */
function handleFilterTagClick(event) {
    const tag = event.currentTarget;
    const filter = tag.dataset.filter;
    const type = tag.dataset.type;
    
    if (filter === 'all') {
        toggleLanguageFilter('all');
    } else if (type === 'language') {
        toggleLanguageFilter(filter);
    } else if (type === 'dialect') {
        toggleDialectFilter(filter);
    }
}

/**
 * 切换语言筛选
 * @param {string} langCode - 语言代码
 */
function toggleLanguageFilter(langCode) {
    if (langCode === 'all') {
        // 如果点击"全部"，清除其他所有筛选
        clearAllFilters();
        document.querySelector('.filter-tag[data-filter="all"]').classList.add('active');
    } else {
        // 取消"全部"的选中状态
        document.querySelector('.filter-tag[data-filter="all"]').classList.remove('active');
        
        // 切换当前语言的选中状态
        const index = currentFilters.language.indexOf(langCode);
        if (index === -1) {
            currentFilters.language.push(langCode);
            document.querySelector(`.filter-tag[data-filter="${langCode}"]`).classList.add('active');
        } else {
            currentFilters.language.splice(index, 1);
            document.querySelector(`.filter-tag[data-filter="${langCode}"]`).classList.remove('active');
        }
    }
    
    // 实时保存筛选状态
    saveFilterState();
    
    // 实时同步到侧边栏和其他页面
    syncFilterState();
    
    renderVoicesList();
}

/**
 * 切换方言筛选
 * @param {string} dialectCode - 方言代码
 */
function toggleDialectFilter(dialectCode) {
    // 取消"全部"的选中状态
    document.querySelector('.filter-tag[data-filter="all"]').classList.remove('active');
    
    // 切换当前方言的选中状态
    const index = currentFilters.dialect.indexOf(dialectCode);
    const dialectTag = document.querySelector(`.filter-tag[data-filter="${dialectCode}"]`);
    
    if (index === -1) {
        currentFilters.dialect.push(dialectCode);
        if (dialectTag) dialectTag.classList.add('active');
    } else {
        currentFilters.dialect.splice(index, 1);
        if (dialectTag) dialectTag.classList.remove('active');
    }
    
    // 实时保存筛选状态
    saveFilterState();
    
    // 实时同步到侧边栏和其他页面
    syncFilterState();
    
    renderVoicesList();
}

/**
 * 实时同步筛选状态到侧边栏和其他页面
 */
function syncFilterState() {
    // 确保当前筛选状态是有效的对象
    if (!currentFilters || typeof currentFilters !== 'object') {
        console.warn('同步筛选状态时发现无效数据，不执行同步');
        return;
    }
    
    chrome.runtime.sendMessage({
        action: 'updateVoiceOptions',
        voiceFilters: currentFilters
    });
}

/**
 * 清除所有筛选条件
 */
function clearAllFilters() {
    // 清除状态
    currentFilters = {
        language: [],
        dialect: []
    };
    
    // 清除UI选中状态
    document.querySelectorAll('.filter-tag').forEach(tag => {
        if (tag.dataset.filter !== 'all') {
            tag.classList.remove('active');
        }
    });
}

/**
 * 关闭语音设置页面
 */
function closeVoiceSettings() {
    // 检查是否在内嵌环境中运行
    if (window.parent !== window) {
        // 在内嵌环境中，使用自定义事件通知父页面关闭浮层
        const event = new CustomEvent('closeVoiceSettings');
        window.dispatchEvent(event);
    } else {
        // 在独立窗口中，直接关闭窗口
        window.close();
    }
}

/**
 * 保存设置并关闭
 */
function saveAndClose() {
    saveFavoriteVoices();
    saveDefaultVoice();
    saveFilterState();
    syncWithSidebar();
    
    // 显示保存成功消息（如果在内嵌环境中）
    if (window.parent !== window) {
        const event = new CustomEvent('voiceSettingsSaved', {
            detail: { message: "语音设置已保存" }
        });
        window.dispatchEvent(event);
    }
    
    closeVoiceSettings();
}

/**
 * 将设置同步到侧边栏
 */
function syncWithSidebar() {
    // 将默认和收藏语音同步到侧边栏下拉框
    chrome.runtime.sendMessage({
        action: 'updateVoiceOptions',
        defaultVoice: defaultVoice,
        favoriteVoices: favoriteVoices,
        voiceFilters: currentFilters
    });
}

// === 工具函数 ===

/**
 * 获取语言名称
 * @param {string} langCode - 语言代码
 * @returns {string} - 语言名称
 */
function getLangName(langCode) {
    const langNames = {
        'zh': '中文',
        'en': '英文',
        'ja': '日文',
        'fr': '法文',
        'de': '德文',
        'es': '西班牙文',
        'it': '意大利文',
        'ru': '俄文',
        'ko': '韩文',
        'other': '其他语言'
    };
    return langNames[langCode] || langCode;
}

/**
 * 获取性别名称
 * @param {string} gender - 性别代码
 * @returns {string} - 性别名称
 */
function getGenderName(gender) {
    const genderNames = {
        'male': '男声',
        'female': '女声'
    };
    return genderNames[gender] || gender;
}

/**
 * 获取方言名称
 * @param {string} dialectCode - 方言代码
 * @returns {string} - 方言名称
 */
function getDialectName(dialectCode) {
    const dialectNames = {
        'yue': '粤语',
        'tw': '台湾国语',
        'us': '美式英语',
        'gb': '英式英语',
        'au': '澳洲英语',
        'in': '印度英语'
    };
    return dialectNames[dialectCode] || dialectCode;
}

/**
 * 从语音名称和语言代码检测方言
 * @param {Object} voice - 语音对象
 * @returns {Object|null} - 方言信息或null
 */
function detectDialect(voice) {
    const name = voice.voiceName.toLowerCase();
    const lang = voice.lang || '';
    
    // 中文方言检测
    if (lang.startsWith('zh')) {
        if (lang === 'zh-HK' || lang === 'zh-yue' || 
            name.includes('粤语') || name.includes('广东') || 
            name.includes('香港') || name.includes('cantonese')) {
            return { code: 'yue', name: '粤语' };
        }
        
        if (lang === 'zh-TW' || name.includes('台湾') || 
            name.includes('taiwan')) {
            return { code: 'tw', name: '台湾国语' };
        }
    }
    
    // 英语区域变体
    if (lang.startsWith('en')) {
        if (lang === 'en-US' || name.includes('american')) {
            return { code: 'us', name: '美式英语' };
        }
        if (lang === 'en-GB' || name.includes('british')) {
            return { code: 'gb', name: '英式英语' };
        }
        if (lang === 'en-AU' || name.includes('australia')) {
            return { code: 'au', name: '澳洲英语' };
        }
        if (lang === 'en-IN' || name.includes('india') || name.includes('indian')) {
            return { code: 'in', name: '印度英语' };
        }
    }
    
    return null;
}

/**
 * 渲染语音列表
 */
function renderVoicesList() {
    // 首先对语音进行排序：默认语音 > 收藏语音 > 其他语音
    const sortedVoices = sortVoicesByPriority(availableVoices);
    
    // 筛选语音
    const filteredVoices = filterVoices(sortedVoices);
    
    // 清空当前列表
    voicesList.innerHTML = '';
    
    // 检查是否为空结果
    if (filteredVoices.length === 0) {
        const noResultsElement = createNoResultsState();
        voicesList.appendChild(noResultsElement);
        return;
    }
    
    // 渲染语音条目
    let lastWasPriority = false; // 跟踪是否需要添加分隔线
    
    filteredVoices.forEach((voice, index) => {
        const isPriority = voice.voiceName === defaultVoice || favoriteVoices.includes(voice.voiceName);
        
        // 在普通语音和优先语音之间添加分隔线
        if (lastWasPriority && !isPriority && index > 0) {
            addSeparator(voicesList);
        }
        
        const voiceElement = createVoiceElement(voice);
        voicesList.appendChild(voiceElement);
        
        lastWasPriority = isPriority;
    });
    
    // 如果筛选后没有语音，显示提示
    if (voicesList.children.length === 0) {
        const emptyState = createEmptyState(
            '未找到语音', 
            '请调整筛选条件或清除搜索内容'
        );
        voicesList.appendChild(emptyState);
    }
}

/**
 * 按优先级对语音进行排序
 * @param {Array} voices - 语音数组
 * @returns {Array} - 排序后的语音数组
 */
function sortVoicesByPriority(voices) {
    return [...voices].sort((a, b) => {
        // 默认语音置顶
        if (a.voiceName === defaultVoice) return -1;
        if (b.voiceName === defaultVoice) return 1;
        
        // 收藏语音次之
        const aIsFavorite = favoriteVoices.includes(a.voiceName);
        const bIsFavorite = favoriteVoices.includes(b.voiceName);
        
        if (aIsFavorite && !bIsFavorite) return -1;
        if (!aIsFavorite && bIsFavorite) return 1;
        
        // 同一优先级按名称排序
        return a.voiceName.localeCompare(b.voiceName);
    });
}

/**
 * 根据筛选条件过滤语音
 * @param {Array} voices - 语音数组
 * @returns {Array} - 筛选后的语音数组
 */
function filterVoices(voices) {
    return voices.filter(voice => {
        // 搜索文本筛选
        if (searchText && !voice.voiceName.toLowerCase().includes(searchText)) {
            return false;
        }
        
        // 如果没有激活的筛选器，或者选择了"全部"，显示所有语音
        if (document.querySelector('.filter-tag[data-filter="all"].active') ||
            (currentFilters.language.length === 0 && 
             currentFilters.dialect.length === 0)) {
            return true;
        }
        
        // 语言筛选
        if (currentFilters.language.length > 0) {
            const voiceLanguage = voice.lang ? voice.lang.split('-')[0] : 'other';
            if (!currentFilters.language.includes(voiceLanguage)) {
                return false;
            }
        }
        
        // 方言筛选
        if (currentFilters.dialect.length > 0) {
            const dialect = detectDialect(voice);
            if (!dialect || !currentFilters.dialect.includes(dialect.code)) {
                return false;
            }
        }
        
        return true;
    });
}

/**
 * 添加分隔线
 * @param {HTMLElement} container - 容器元素
 */
function addSeparator(container) {
    const separator = document.createElement('div');
    separator.className = 'separator';
    container.appendChild(separator);
}

/**
 * 创建语音元素
 * @param {Object} voice - 语音对象
 * @returns {HTMLElement} - 语音DOM元素
 */
function createVoiceElement(voice) {
    const clone = voiceItemTemplate.content.cloneNode(true);
    const voiceItem = clone.querySelector('.voice-item');
    const voiceName = voiceItem.querySelector('.voice-name');
    const priorityIndicator = voiceItem.querySelector('.priority-indicator');
    const previewBtn = voiceItem.querySelector('.preview-btn');
    const favoriteBtn = voiceItem.querySelector('.favorite-btn');
    const defaultBtn = voiceItem.querySelector('.default-btn');
    const voiceTags = voiceItem.querySelector('.voice-tags');
    
    // 设置数据属性
    voiceItem.dataset.voiceName = voice.voiceName;
    
    // 设置语音名称
    voiceName.textContent = voice.voiceName;
    
    // 检查是否为优先语音（默认或收藏）
    const isDefault = voice.voiceName === defaultVoice;
    const isFavorite = favoriteVoices.includes(voice.voiceName);
    
    if (isDefault || isFavorite) {
        voiceItem.classList.add('priority');
        priorityIndicator.style.display = 'inline';
    }
    
    // 设置按钮状态
    if (isFavorite) {
        favoriteBtn.textContent = '★';
        favoriteBtn.title = '取消收藏';
        favoriteBtn.classList.add('favorite');
    }
    
    if (isDefault) {
        defaultBtn.textContent = '✓';
        defaultBtn.title = '当前默认';
        defaultBtn.classList.add('default');
    }
    
    // 添加语音标签
    addVoiceTags(voice, voiceTags);
    
    // 添加事件监听器
    previewBtn.addEventListener('click', () => previewVoice(voice.voiceName));
    favoriteBtn.addEventListener('click', () => toggleFavorite(voice.voiceName));
    defaultBtn.addEventListener('click', () => setAsDefault(voice.voiceName));
    
    return voiceItem;
}

/**
 * 为语音添加特性标签
 * @param {Object} voice - 语音对象
 * @param {HTMLElement} container - 标签容器
 */
function addVoiceTags(voice, container) {
    // 添加语言标签
    if (voice.lang) {
        const langCode = voice.lang.split('-')[0];
        const langName = getLangName(langCode);
        const langTag = createTag(`语言: ${langName}`, getLangIcon(langCode));
        container.appendChild(langTag);
    }
    
    // 添加性别标签
    if (voice.gender) {
        const genderName = getGenderName(voice.gender);
        const genderTag = createTag(`性别: ${genderName}`, getGenderIcon(voice.gender));
        container.appendChild(genderTag);
    }
    
    // 添加方言标签
    const dialect = detectDialect(voice);
    if (dialect) {
        const dialectTag = createTag(`方言: ${dialect.name}`);
        container.appendChild(dialectTag);
    }
}

/**
 * 创建标签元素
 * @param {string} text - 标签文本
 * @param {string} [icon] - 标签图标（可选）
 * @returns {HTMLElement} - 标签DOM元素
 */
function createTag(text, icon = null) {
    const tag = document.createElement('span');
    tag.className = 'voice-tag';
    
    if (icon) {
        const iconSpan = document.createElement('span');
        iconSpan.className = 'tag-icon';
        iconSpan.textContent = icon;
        tag.appendChild(iconSpan);
    }
    
    const textSpan = document.createElement('span');
    textSpan.textContent = text;
    tag.appendChild(textSpan);
    
    return tag;
}

/**
 * 获取语言图标
 * @param {string} langCode - 语言代码
 * @returns {string} - 语言图标
 */
function getLangIcon(langCode) {
    const langIcons = {
        'zh': '🇨🇳',
        'en': '🇬🇧',
        'ja': '🇯🇵',
        'fr': '🇫🇷',
        'de': '🇩🇪',
        'es': '🇪🇸',
        'it': '🇮🇹',
        'ru': '🇷🇺',
        'ko': '🇰🇷'
    };
    return langIcons[langCode] || '🌐';
}

/**
 * 获取性别图标
 * @param {string} gender - 性别代码
 * @returns {string} - 性别图标
 */
function getGenderIcon(gender) {
    return gender === 'male' ? '♂' : '♀';
}

/**
 * 试听语音
 * @param {string} voiceName - 语音名称
 */
function previewVoice(voiceName) {
    const voiceItem = document.querySelector(`.voice-item[data-voice-name="${voiceName}"]`);
    const previewBtn = voiceItem.querySelector('.preview-btn');
    
    // 停止当前正在播放的语音
    chrome.tts.stop();
    
    // 更新所有语音项的状态
    document.querySelectorAll('.voice-item').forEach(item => {
        item.classList.remove('previewing');
        item.querySelector('.preview-btn').innerHTML = '🔊';
    });
    
    // 更新UI为播放状态
    previewBtn.innerHTML = createAudioWaveHTML();
    voiceItem.classList.add('previewing');
    
    // 获取语言相关的示例文本
    const voice = availableVoices.find(v => v.voiceName === voiceName);
    const sampleText = getSampleText(voice.lang ? voice.lang.split('-')[0] : 'default');
    
    // 播放语音
    chrome.tts.speak(
        sampleText,
        {
            voiceName: voiceName,
            rate: 1.0,
            onEvent: function(event) {
                if (event.type === 'end' || event.type === 'interrupted' || event.type === 'error') {
                    // 恢复UI
                    previewBtn.innerHTML = '🔊';
                    voiceItem.classList.remove('previewing');
                }
            }
        }
    );
}

/**
 * 创建音频波形HTML
 * @returns {string} - 音频波形HTML
 */
function createAudioWaveHTML() {
    return `
        <div class="audio-wave">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
        </div>
    `;
}

/**
 * 获取试听文本
 * @param {string} langCode - 语言代码
 * @returns {string} - 试听文本
 */
function getSampleText(langCode) {
    const sampleTexts = {
        'zh': '这是一段示例文本，用于测试语音效果。',
        'en': 'This is a sample text for testing voice quality.',
        'ja': 'これは音声品質をテストするためのサンプルテキストです。',
        'fr': 'Ceci est un exemple de texte pour tester la qualité de la voix.',
        'de': 'Dies ist ein Beispieltext zum Testen der Sprachqualität.',
        'default': 'Testing voice sample.'
    };
    
    return sampleTexts[langCode] || sampleTexts.default;
}

/**
 * 切换语音收藏状态
 * @param {string} voiceName - 语音名称
 */
function toggleFavorite(voiceName) {
    const voiceItem = document.querySelector(`.voice-item[data-voice-name="${voiceName}"]`);
    const favoriteBtn = voiceItem.querySelector('.favorite-btn');
    const isFavorite = favoriteVoices.includes(voiceName);
    
    if (!isFavorite) {
        // 添加到收藏
        favoriteVoices.push(voiceName);
        
        // 动画效果
        voiceItem.classList.add('animating');
        
        // 更新UI
        favoriteBtn.textContent = '★';
        favoriteBtn.title = '取消收藏';
        favoriteBtn.classList.add('favorite');
        voiceItem.classList.add('priority');
        voiceItem.querySelector('.priority-indicator').style.display = 'inline';
        
        // 高亮动画
        voiceItem.classList.add('highlight');
        setTimeout(() => {
            voiceItem.classList.remove('highlight');
            voiceItem.classList.remove('animating');
        }, 500);
    } else {
        // 从收藏中移除
        const index = favoriteVoices.indexOf(voiceName);
        favoriteVoices.splice(index, 1);
        
        // 淡出动画
        voiceItem.classList.add('fading-out');
        
        // 更新UI
        favoriteBtn.textContent = '☆';
        favoriteBtn.title = '收藏';
        favoriteBtn.classList.remove('favorite');
        
        // 如果不是默认语音，移除优先级样式
        if (voiceName !== defaultVoice) {
            voiceItem.classList.remove('priority');
            voiceItem.querySelector('.priority-indicator').style.display = 'none';
        }
        
        setTimeout(() => {
            voiceItem.classList.remove('fading-out');
        }, 200);
    }
    
    // 重新渲染以保持排序
    // 这里延迟一点以便动画完成
    setTimeout(() => {
        renderVoicesList();
    }, 300);
}

/**
 * 设置默认语音
 * @param {string} voiceName - 语音名称
 */
function setAsDefault(voiceName) {
    // 如果点击的是当前默认语音，不做操作
    if (voiceName === defaultVoice) {
        return;
    }
    
    // 获取原默认语音元素和新默认语音元素
    const oldDefaultItem = document.querySelector(`.voice-item[data-voice-name="${defaultVoice}"]`);
    const newDefaultItem = document.querySelector(`.voice-item[data-voice-name="${voiceName}"]`);
    
    // 更新模型中的默认语音
    defaultVoice = voiceName;
    
    // 如果原默认语音元素存在，更新其UI
    if (oldDefaultItem) {
        const oldDefaultBtn = oldDefaultItem.querySelector('.default-btn');
        oldDefaultBtn.textContent = '○';
        oldDefaultBtn.title = '设为默认';
        oldDefaultBtn.classList.remove('default');
        
        // 如果不是收藏语音，移除优先级样式
        if (!favoriteVoices.includes(oldDefaultItem.dataset.voiceName)) {
            oldDefaultItem.classList.remove('priority');
            oldDefaultItem.querySelector('.priority-indicator').style.display = 'none';
        }
    }
    
    // 更新新默认语音的UI
    const newDefaultBtn = newDefaultItem.querySelector('.default-btn');
    newDefaultBtn.textContent = '✓';
    newDefaultBtn.title = '当前默认';
    newDefaultBtn.classList.add('default');
    newDefaultItem.classList.add('priority');
    newDefaultItem.querySelector('.priority-indicator').style.display = 'inline';
    
    // 添加高亮动画
    newDefaultItem.classList.add('highlight');
    setTimeout(() => {
        newDefaultItem.classList.remove('highlight');
    }, 500);
    
    // 重新渲染以保持排序
    // 这里延迟一点以便动画完成
    setTimeout(() => {
        renderVoicesList();
    }, 300);
} 