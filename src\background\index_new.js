// 神灯AI·灵阅 - 模块化后台脚本主入口文件
console.log("🚀 神灯AI·灵阅 v0.52 - 模块化后台脚本启动");

// 导入模块系统
import { backgroundModuleContainer, registerAllBackgroundModules } from './modules/BackgroundModuleContainer.js';

// 全局状态
let isInitialized = false;
let initializationPromise = null;
let startTime = Date.now();

// 主初始化函数
async function initializeBackground() {
    if (isInitialized) {
        console.log('后台脚本已经初始化过了');
        return;
    }
    
    if (initializationPromise) {
        console.log('后台脚本正在初始化中，等待完成...');
        return initializationPromise;
    }
    
    console.log('🔄 开始初始化模块化后台脚本...');
    
    initializationPromise = performInitialization();
    
    try {
        await initializationPromise;
        isInitialized = true;
        console.log('✅ 模块化后台脚本初始化完成');
    } catch (error) {
        console.error('❌ 模块化后台脚本初始化失败:', error);
        isInitialized = false;
        initializationPromise = null;
        throw error;
    }
}

// 执行初始化
async function performInitialization() {
    try {
        // 1. 设置依赖
        console.log('🔗 设置模块依赖...');
        backgroundModuleContainer.setDependencies({
            chrome: chrome,
            startTime: startTime
        });
        
        // 2. 注册所有模块
        console.log('📦 注册后台模块...');
        const registrationSuccess = registerAllBackgroundModules(backgroundModuleContainer);
        if (!registrationSuccess) {
            throw new Error('后台模块注册失败');
        }
        
        // 3. 初始化所有模块
        console.log('🚀 初始化后台模块...');
        const initResults = await backgroundModuleContainer.initializeAll();
        
        // 4. 检查初始化结果
        const failedModules = initResults.filter(result => !result.success);
        if (failedModules.length > 0) {
            console.warn('⚠️ 部分后台模块初始化失败:', failedModules);
            // 不抛出错误，允许部分功能正常工作
        }
        
        // 5. 设置全局错误处理
        setupGlobalErrorHandling();
        
        // 6. 设置扩展生命周期处理
        setupExtensionLifecycle();
        
        // 7. 启动健康检查
        startHealthCheck();
        
        // 8. 设置调试工具
        setupDebugTools();

        // 9. 设置插件图标点击处理
        setupActionHandler();

        console.log('🎉 所有后台模块初始化完成');
        
    } catch (error) {
        console.error('❌ 后台初始化过程中发生错误:', error);
        throw error;
    }
}

// 设置全局错误处理
function setupGlobalErrorHandling() {
    // 捕获未处理的Promise拒绝
    self.addEventListener('unhandledrejection', (event) => {
        console.error('❌ 未处理的Promise拒绝:', event.reason);
        
        // 通知错误处理模块
        const messageHandler = backgroundModuleContainer.getModule('MessageHandler');
        if (messageHandler) {
            messageHandler.broadcast({
                action: 'error',
                error: {
                    type: 'unhandledrejection',
                    message: event.reason?.message || '未知错误',
                    stack: event.reason?.stack
                }
            });
        }
    });

    // 捕获全局错误
    self.addEventListener('error', (event) => {
        console.error('❌ 全局错误:', event.error);
        
        // 通知错误处理模块
        const messageHandler = backgroundModuleContainer.getModule('MessageHandler');
        if (messageHandler) {
            messageHandler.broadcast({
                action: 'error',
                error: {
                    type: 'error',
                    message: event.error?.message || '未知错误',
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error?.stack
                }
            });
        }
    });

    console.log('🛡️ 全局错误处理已设置');
}

// 设置扩展生命周期处理
function setupExtensionLifecycle() {
    // 扩展启动
    chrome.runtime.onStartup.addListener(() => {
        console.log('🚀 扩展启动');
        notifyModules('extensionStartup');
    });

    // 扩展安装
    chrome.runtime.onInstalled.addListener((details) => {
        console.log('📦 扩展安装/更新:', details.reason);
        notifyModules('extensionInstalled', details);
        
        // 首次安装时的初始化
        if (details.reason === 'install') {
            handleFirstInstall();
        } else if (details.reason === 'update') {
            handleUpdate(details.previousVersion);
        }
    });

    // 扩展挂起
    chrome.runtime.onSuspend.addListener(() => {
        console.log('😴 扩展即将挂起');
        notifyModules('extensionSuspend');
    });

    // 扩展挂起取消
    chrome.runtime.onSuspendCanceled.addListener(() => {
        console.log('😊 扩展挂起已取消');
        notifyModules('extensionSuspendCanceled');
    });

    console.log('🔄 扩展生命周期处理已设置');
}

// 通知所有模块
function notifyModules(eventType, data = null) {
    const modules = backgroundModuleContainer.getAllModules();
    modules.forEach(module => {
        if (typeof module.onEvent === 'function') {
            try {
                module.onEvent(eventType, data);
            } catch (error) {
                console.error(`❌ 通知模块 ${module.name} 事件失败:`, error);
            }
        }
    });
    
    // 也通过容器事件系统广播
    backgroundModuleContainer.emit(eventType, data);
}

// 处理首次安装
function handleFirstInstall() {
    console.log('🎉 首次安装，执行初始化设置');
    
    // 设置默认配置
    const defaultConfig = {
        'debug.useNewModules': false, // 默认不启用新模块系统
        'tts.defaultSpeed': 1.1,
        'tts.continuousReading': true,
        'tts.maxChapters': 30
    };
    
    Object.entries(defaultConfig).forEach(([key, value]) => {
        configManager.setConfig(key, value);
    });
    
    console.log('✅ 默认配置已设置');
}

// 处理更新
function handleUpdate(previousVersion) {
    console.log(`🔄 从版本 ${previousVersion} 更新到当前版本`);
    
    // 这里可以添加版本迁移逻辑
    // 例如：配置迁移、数据格式升级等
}

// 启动健康检查
function startHealthCheck() {
    // 每5分钟进行一次健康检查
    setInterval(async () => {
        try {
            const healthReport = await backgroundModuleContainer.healthCheck();
            const unhealthyModules = Object.entries(healthReport)
                .filter(([name, status]) => status.status !== 'ok');
            
            if (unhealthyModules.length > 0) {
                console.warn('⚠️ 发现不健康的模块:', unhealthyModules);
                
                // 尝试重启不健康的模块
                for (const [moduleName] of unhealthyModules) {
                    try {
                        console.log(`🔄 尝试重启模块: ${moduleName}`);
                        await backgroundModuleContainer.restartModule(moduleName);
                    } catch (error) {
                        console.error(`❌ 重启模块 ${moduleName} 失败:`, error);
                    }
                }
            }
        } catch (error) {
            console.error('❌ 健康检查失败:', error);
        }
    }, 5 * 60 * 1000);
    
    console.log('🏥 健康检查已启动');
}

// 设置插件图标点击处理
function setupActionHandler() {
    try {
        console.log('🔧 设置插件图标点击处理器');

        chrome.action.onClicked.addListener((tab) => {
            console.log('🎯 插件图标被点击 - 标签页信息:', {
                id: tab.id,
                windowId: tab.windowId,
                url: tab.url
            });

            if (!tab?.windowId) {
                console.error('❌ 无效的标签页 - 缺少windowId');
                return;
            }

            // 检查sidePanel API是否可用
            if (typeof chrome.sidePanel !== 'undefined') {
                console.log('📱 尝试打开侧边栏...');

                chrome.sidePanel.open({windowId: tab.windowId})
                    .then(() => {
                        console.log('✅ 侧边栏打开成功');

                        // 通知状态管理器
                        const stateManager = backgroundModuleContainer.getModule('StateManager');
                        if (stateManager) {
                            stateManager.setCurrentTabId(tab.id, 'action_click');
                        }
                    })
                    .catch((error) => {
                        console.error('❌ 打开侧边栏失败:', error);
                    });
            } else {
                console.warn('⚠️ sidePanel API不可用，无法打开侧边栏');
            }
        });

        console.log('✅ 插件图标点击处理器已设置');

    } catch (error) {
        console.error('❌ 设置插件图标点击处理器失败:', error);
    }
}

// 设置调试工具
function setupDebugTools() {
    // 全局调试函数
    self.getBackgroundModuleContainer = () => backgroundModuleContainer;

    self.getBackgroundModule = (name) => backgroundModuleContainer.getModule(name);

    self.getBackgroundModuleStatus = () => {
        console.log('📊 后台模块系统状态:', backgroundModuleContainer.getStatus());
        return backgroundModuleContainer.getStatus();
    };

    self.restartBackgroundModule = async (name) => {
        try {
            const result = await backgroundModuleContainer.restartModule(name);
            console.log(`✅ 后台模块 ${name} 重启${result.success ? '成功' : '失败'}`);
            return result;
        } catch (error) {
            console.error(`❌ 后台模块 ${name} 重启失败:`, error);
            return { success: false, error: error.message };
        }
    };

    self.toggleBackgroundModule = (name, enabled) => {
        const success = enabled ?
            backgroundModuleContainer.enableModule(name) :
            backgroundModuleContainer.disableModule(name);

        console.log(`${success ? '✅' : '❌'} 后台模块 ${name} ${enabled ? '启用' : '禁用'}${success ? '成功' : '失败'}`);
        return success;
    };

    self.getBackgroundHealthReport = async () => {
        const report = await backgroundModuleContainer.healthCheck();
        console.log('🏥 后台模块健康报告:', report);
        return report;
    };

    self.getBackgroundPerformanceReport = () => {
        const report = backgroundModuleContainer.getPerformanceReport();
        console.log('📈 后台模块性能报告:', report);
        return report;
    };

    console.log('🛠️ 后台调试工具已设置');
    console.log('💡 可用命令:');
    console.log('  getBackgroundModuleContainer() - 获取后台模块容器');
    console.log('  getBackgroundModule(name) - 获取指定后台模块');
    console.log('  getBackgroundModuleStatus() - 查看后台模块状态');
    console.log('  restartBackgroundModule(name) - 重启指定后台模块');
    console.log('  toggleBackgroundModule(name, enabled) - 启用/禁用后台模块');
    console.log('  getBackgroundHealthReport() - 获取健康报告');
    console.log('  getBackgroundPerformanceReport() - 获取性能报告');
}

// 错误处理
function handleInitializationError(error) {
    console.error('❌ 后台脚本初始化失败:', error);
    
    // 发送错误通知到前端
    try {
        chrome.runtime.sendMessage({
            action: 'backgroundInitError',
            error: {
                message: error.message,
                stack: error.stack,
                timestamp: Date.now()
            }
        });
    } catch (e) {
        // 忽略发送失败
    }
}



// 主启动逻辑
async function main() {
    try {
        console.log('✅ 启动后台模块化系统');

        // 初始化后台脚本
        await initializeBackground();

    } catch (error) {
        console.error('❌ 后台模块化系统启动失败:', error);
    }
}

// 启动应用
main();

// 导出主要函数供外部使用
export {
    initializeBackground,
    backgroundModuleContainer
};
