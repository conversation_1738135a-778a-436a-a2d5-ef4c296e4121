# 🔍 代码规范检查清单

## 📋 Chrome Web Store & Microsoft Edge Add-ons 合规检查

### 🚨 必须修复的问题

#### 1. 控制台日志清理
**当前问题**：代码中存在大量调试用的console.log
**影响**：可能被商店认为是调试代码，影响审核
**修复优先级**：🔴 高

**需要处理的文件**：
- `src/background/index.js` - 200+ console.log语句
- `src/ui/sidepanel/index.js` - 150+ console.log语句
- `src/content/parser.js` - 30+ console.log语句
- `src/ui/sidepanel/modules/*.js` - 各模块中的调试日志

**修复策略**：
```javascript
// 保留错误和警告日志
console.error("重要错误信息");
console.warn("重要警告信息");

// 移除或条件化调试日志
// 移除：console.log("调试信息");
// 或改为：if (DEBUG_MODE) console.log("调试信息");
```

#### 2. 未使用变量清理
**当前问题**：ESLint报告多个未使用的变量
**影响**：代码质量问题，可能影响审核
**修复优先级**：🔴 高

**需要修复的变量**：
- `src/background/index.js`: key, previousState, activeWindowId, response, injectionResult, removeInfo, tab
- `src/ui/sidepanel/index.js`: event, maxPreviewLength, loadingMessage, voiceInfo等

#### 3. 权限最小化
**当前问题**：manifest.json中可能存在过度权限申请
**影响**：商店审核的重点检查项
**修复优先级**：🔴 高

**需要审查的权限**：
```json
{
  "permissions": [
    "storage",           // ✅ 必需 - 保存用户设置
    "activeTab",         // ✅ 必需 - 获取当前页面
    "scripting",         // ✅ 必需 - 注入内容脚本
    "tts",              // ✅ 必需 - 语音合成
    "sidePanel"         // ✅ 必需 - 侧边栏功能
  ],
  "host_permissions": [
    "<all_urls>"        // ⚠️ 需要说明 - 解析任意网页内容
  ]
}
```

#### 4. 内容安全策略(CSP)
**当前问题**：需要确保CSP合规
**影响**：安全审核的关键项
**修复优先级**：🔴 高

**检查项目**：
- [ ] 无内联脚本 (`<script>` 标签内容)
- [ ] 无内联样式 (`style` 属性)
- [ ] 无 `eval()` 使用
- [ ] 所有外部资源使用HTTPS

### 🟡 建议修复的问题

#### 5. 错误处理完善
**当前问题**：部分异步操作缺少错误处理
**影响**：用户体验和稳定性
**修复优先级**：🟡 中

**需要改进的地方**：
```javascript
// 当前代码
chrome.runtime.sendMessage({action: 'test'});

// 改进后
chrome.runtime.sendMessage({action: 'test'}, (response) => {
  if (chrome.runtime.lastError) {
    console.error('消息发送失败:', chrome.runtime.lastError);
    // 处理错误情况
  }
});
```

#### 6. 异步操作规范化
**当前问题**：混用Promise和回调
**影响**：代码一致性
**修复优先级**：🟡 中

#### 7. 国际化准备
**当前问题**：所有文本都是中文硬编码
**影响**：国际市场适用性
**修复优先级**：🟡 中

### 🔵 可选优化项目

#### 8. 性能优化
- 减少DOM操作频率
- 优化事件监听器管理
- 改进内存使用

#### 9. 代码分割
- 将大文件拆分为模块
- 按需加载功能
- 减少初始加载时间

## 🛠️ 具体修复计划

### 阶段1：关键合规性修复（1-2天）

#### 1.1 清理调试日志
```bash
# 创建生产版本的日志管理
const DEBUG_MODE = false; // 生产环境设为false

function debugLog(...args) {
  if (DEBUG_MODE) {
    console.log(...args);
  }
}

function errorLog(...args) {
  console.error(...args); // 保留错误日志
}
```

#### 1.2 修复未使用变量
- 移除未使用的变量声明
- 重构函数参数，移除未使用参数
- 添加ESLint忽略注释（必要时）

#### 1.3 权限审查和说明
```json
{
  "permissions": [
    "storage",
    "activeTab", 
    "scripting",
    "tts",
    "sidePanel"
  ],
  "host_permissions": [
    "<all_urls>"
  ],
  "permissions_description": {
    "storage": "保存用户的语音设置和阅读偏好",
    "activeTab": "获取当前网页内容进行朗读",
    "scripting": "在网页中注入内容解析脚本",
    "tts": "使用浏览器的语音合成功能",
    "sidePanel": "在浏览器侧边栏显示控制界面",
    "host_permissions": "解析任意网页的文本内容进行朗读"
  }
}
```

### 阶段2：代码质量提升（2-3天）

#### 2.1 错误处理标准化
```javascript
// 统一错误处理函数
function handleChromeError(operation, callback) {
  return (...args) => {
    if (chrome.runtime.lastError) {
      console.error(`${operation} 失败:`, chrome.runtime.lastError);
      if (callback) callback(chrome.runtime.lastError);
      return false;
    }
    return true;
  };
}
```

#### 2.2 异步操作Promise化
```javascript
// 将Chrome API包装为Promise
function sendMessageAsync(message) {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(message, (response) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(response);
      }
    });
  });
}
```

### 阶段3：用户体验优化（1-2天）

#### 3.1 加载状态管理
- 添加加载指示器
- 优化状态转换动画
- 改进错误提示

#### 3.2 界面响应性
- 防抖处理用户输入
- 优化大列表渲染
- 减少重绘重排

## 📊 质量检查工具

### 1. ESLint配置
```json
{
  "extends": ["eslint:recommended"],
  "env": {
    "browser": true,
    "es2021": true,
    "webextensions": true
  },
  "rules": {
    "no-console": ["warn", {"allow": ["error", "warn"]}],
    "no-unused-vars": "error",
    "no-undef": "error"
  }
}
```

### 2. 自动化检查脚本
```bash
#!/bin/bash
# 代码质量检查脚本

echo "🔍 开始代码质量检查..."

# ESLint检查
echo "📋 运行ESLint..."
npx eslint src/ --ext .js

# 查找console.log
echo "🔍 检查调试日志..."
grep -r "console\.log" src/ || echo "✅ 无调试日志"

# 检查未使用文件
echo "📁 检查未使用文件..."
# 自定义脚本检查文件引用

echo "✅ 代码质量检查完成"
```

## 🎯 验收标准

### 技术标准
- [ ] ESLint检查0错误0警告
- [ ] 无调试console.log
- [ ] 所有异步操作有错误处理
- [ ] 权限使用有明确说明

### 商店合规标准
- [ ] 功能描述与实际一致
- [ ] 无误导性功能声明
- [ ] 隐私政策完整
- [ ] 用户数据处理透明

### 性能标准
- [ ] 初始加载<3秒
- [ ] 内存使用稳定
- [ ] 无内存泄漏
- [ ] UI响应<100ms

## 📞 下一步行动

1. **立即开始**：清理调试日志和未使用变量
2. **本周完成**：权限审查和CSP检查
3. **下周完成**：错误处理和性能优化
4. **最终检查**：使用自动化工具验证

这个检查清单将确保神灯AI·灵阅符合商店上架要求，提高审核通过率。
