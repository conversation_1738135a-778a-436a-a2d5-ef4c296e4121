// Tesseract.js configuration for OCR processing
// This file initializes Tesseract.js with the necessary worker and language settings

// Initialize Tesseract.js with worker source
Tesseract.workerPath = chrome.runtime.getURL('src/lib/tesseract/worker.min.js');
Tesseract.corePath = chrome.runtime.getURL('src/lib/tesseract/tesseract-core.wasm.js');
Tesseract.langPath = chrome.runtime.getURL('src/lib/tesseract/lang');

// Default language configuration
Tesseract.recognizeOptions = {
  lang: 'chi_sim', // Simplified Chinese as default language
  logger: (m) => console.log(m)
};

export default Tesseract;
