// 错误处理器 - 统一错误处理和日志记录
class ErrorHandler {
    constructor() {
        this.errorLog = [];
        this.maxLogSize = 500;
        this.errorCounts = new Map();
        this.retryStrategies = new Map();
        this.setupGlobalErrorHandling();
    }

    // 设置全局错误处理
    setupGlobalErrorHandling() {
        // 捕获未处理的Promise拒绝
        if (typeof window !== 'undefined') {
            window.addEventListener('unhandledrejection', (event) => {
                this.handleError(event.reason, 'unhandled_promise_rejection', {
                    promise: event.promise
                });
            });

            // 捕获全局JavaScript错误
            window.addEventListener('error', (event) => {
                this.handleError(event.error, 'global_error', {
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                });
            });
        }
    }

    // 主要错误处理方法
    handleError(error, context = 'unknown', metadata = {}) {
        const errorInfo = this.createErrorInfo(error, context, metadata);
        
        // 记录错误
        this.logError(errorInfo);
        
        // 更新错误计数
        this.updateErrorCount(errorInfo.type);
        
        // 根据错误类型执行相应处理
        this.processError(errorInfo);
        
        return errorInfo;
    }

    // 创建错误信息对象
    createErrorInfo(error, context, metadata) {
        const timestamp = new Date().toISOString();
        const errorType = this.classifyError(error);
        
        return {
            id: `${timestamp}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp,
            type: errorType,
            context,
            message: error?.message || String(error),
            stack: error?.stack || null,
            metadata,
            severity: this.determineSeverity(errorType, context),
            handled: false
        };
    }

    // 错误分类
    classifyError(error) {
        if (!error) return 'unknown';
        
        const message = error.message || String(error);
        const stack = error.stack || '';
        
        // 网络错误
        if (message.includes('fetch') || message.includes('network') || 
            message.includes('NetworkError') || message.includes('Failed to fetch')) {
            return 'network_error';
        }
        
        // Chrome扩展API错误
        if (message.includes('chrome.') || stack.includes('chrome-extension://')) {
            return 'extension_api_error';
        }
        
        // TTS相关错误
        if (message.includes('speechSynthesis') || message.includes('speak') ||
            context.includes('tts') || context.includes('voice')) {
            return 'tts_error';
        }
        
        // 存储错误
        if (message.includes('storage') || message.includes('quota')) {
            return 'storage_error';
        }
        
        // DOM操作错误
        if (message.includes('DOM') || message.includes('Element') ||
            message.includes('querySelector')) {
            return 'dom_error';
        }
        
        // 解析错误
        if (message.includes('parse') || message.includes('JSON') ||
            message.includes('SyntaxError')) {
            return 'parse_error';
        }
        
        // 权限错误
        if (message.includes('permission') || message.includes('denied')) {
            return 'permission_error';
        }
        
        return 'general_error';
    }

    // 确定错误严重程度
    determineSeverity(errorType, context) {
        const criticalErrors = ['storage_error', 'extension_api_error'];
        const warningErrors = ['network_error', 'tts_error'];
        
        if (criticalErrors.includes(errorType)) {
            return 'critical';
        } else if (warningErrors.includes(errorType)) {
            return 'warning';
        } else if (context.includes('background') || context.includes('core')) {
            return 'high';
        }
        
        return 'low';
    }

    // 记录错误
    logError(errorInfo) {
        this.errorLog.push(errorInfo);
        
        // 限制日志大小
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog.shift();
        }
        
        // 控制台输出
        this.logToConsole(errorInfo);
    }

    // 控制台日志输出
    logToConsole(errorInfo) {
        const { severity, type, message, context, timestamp } = errorInfo;
        const logMessage = `[${timestamp}] ${type.toUpperCase()} in ${context}: ${message}`;
        
        switch (severity) {
            case 'critical':
                console.error('🔴', logMessage, errorInfo);
                break;
            case 'high':
                console.error('🟠', logMessage, errorInfo);
                break;
            case 'warning':
                console.warn('🟡', logMessage, errorInfo);
                break;
            default:
                console.log('🔵', logMessage, errorInfo);
        }
    }

    // 处理错误
    processError(errorInfo) {
        const { type, severity, context } = errorInfo;
        
        // 标记为已处理
        errorInfo.handled = true;
        
        // 根据错误类型执行特定处理
        switch (type) {
            case 'network_error':
                this.handleNetworkError(errorInfo);
                break;
            case 'tts_error':
                this.handleTTSError(errorInfo);
                break;
            case 'storage_error':
                this.handleStorageError(errorInfo);
                break;
            case 'extension_api_error':
                this.handleExtensionAPIError(errorInfo);
                break;
            default:
                this.handleGenericError(errorInfo);
        }
        
        // 发送错误通知（如果是严重错误）
        if (severity === 'critical' || severity === 'high') {
            this.notifyError(errorInfo);
        }
    }

    // 处理网络错误
    handleNetworkError(errorInfo) {
        // 可以实现重试逻辑
        console.log('处理网络错误:', errorInfo.message);
    }

    // 处理TTS错误
    handleTTSError(errorInfo) {
        // 可以尝试重置TTS或切换语音
        console.log('处理TTS错误:', errorInfo.message);
    }

    // 处理存储错误
    handleStorageError(errorInfo) {
        // 可以清理存储或提示用户
        console.log('处理存储错误:', errorInfo.message);
    }

    // 处理扩展API错误
    handleExtensionAPIError(errorInfo) {
        // 可以检查权限或重新初始化
        console.log('处理扩展API错误:', errorInfo.message);
    }

    // 处理通用错误
    handleGenericError(errorInfo) {
        console.log('处理通用错误:', errorInfo.message);
    }

    // 更新错误计数
    updateErrorCount(errorType) {
        const count = this.errorCounts.get(errorType) || 0;
        this.errorCounts.set(errorType, count + 1);
    }

    // 错误通知
    notifyError(errorInfo) {
        // 可以发送到UI显示错误提示
        if (typeof window !== 'undefined' && window.postMessage) {
            window.postMessage({
                type: 'ERROR_NOTIFICATION',
                error: {
                    message: errorInfo.message,
                    severity: errorInfo.severity,
                    timestamp: errorInfo.timestamp
                }
            }, '*');
        }
    }

    // 包装异步函数以自动处理错误
    wrapAsync(func, context = 'async_operation') {
        return async (...args) => {
            try {
                return await func.apply(this, args);
            } catch (error) {
                this.handleError(error, context, { args });
                throw error; // 重新抛出以保持原有行为
            }
        };
    }

    // 包装同步函数以自动处理错误
    wrapSync(func, context = 'sync_operation') {
        return (...args) => {
            try {
                return func.apply(this, args);
            } catch (error) {
                this.handleError(error, context, { args });
                throw error;
            }
        };
    }

    // 重试机制
    async retry(operation, maxRetries = 3, delay = 1000, context = 'retry_operation') {
        let lastError;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                
                this.handleError(error, `${context}_attempt_${attempt}`, {
                    attempt,
                    maxRetries
                });
                
                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, delay * attempt));
                }
            }
        }
        
        throw lastError;
    }

    // 获取错误统计
    getErrorStats() {
        const stats = {
            totalErrors: this.errorLog.length,
            errorsByType: Object.fromEntries(this.errorCounts),
            recentErrors: this.errorLog.slice(-10),
            errorsBySeverity: {}
        };
        
        // 按严重程度统计
        this.errorLog.forEach(error => {
            const severity = error.severity;
            stats.errorsBySeverity[severity] = (stats.errorsBySeverity[severity] || 0) + 1;
        });
        
        return stats;
    }

    // 清理错误日志
    clearErrorLog() {
        this.errorLog = [];
        this.errorCounts.clear();
    }

    // 导出错误日志
    exportErrorLog() {
        return {
            timestamp: new Date().toISOString(),
            errors: this.errorLog,
            stats: this.getErrorStats()
        };
    }

    // 检查是否有严重错误
    hasCriticalErrors() {
        return this.errorLog.some(error => error.severity === 'critical');
    }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler();

// 便捷的错误处理函数
export const handleError = (error, context, metadata) => 
    errorHandler.handleError(error, context, metadata);

export const wrapAsync = (func, context) => 
    errorHandler.wrapAsync(func, context);

export const wrapSync = (func, context) => 
    errorHandler.wrapSync(func, context);

export const retry = (operation, maxRetries, delay, context) => 
    errorHandler.retry(operation, maxRetries, delay, context);

console.log("错误处理器已初始化");
