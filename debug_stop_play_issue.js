// 调试停止播放问题的脚本
// 在浏览器控制台中运行此脚本来检查状态

console.log("=== 停止播放问题调试脚本 ===");

// 检查当前状态
chrome.runtime.sendMessage({ action: 'getState' }, (response) => {
    console.log("当前状态:", response);
    
    if (response && response.state) {
        console.log("readingState:", response.state.readingState);
        console.log("currentReadingTabId:", response.state.currentReadingTabId);
        console.log("article:", response.state.article ? "存在" : "不存在");
    }
});

// 检查存储中的状态
chrome.storage.local.get(['readingState', 'currentReadingTabId', 'currentUtterance'], (data) => {
    console.log("存储中的状态:", data);
});

// 检查缓存
chrome.runtime.sendMessage({ action: 'debugInfo' }, (response) => {
    console.log("调试信息:", response);
});

// 模拟停止播放问题的测试序列
function testStopPlaySequence() {
    console.log("开始测试停止播放序列...");
    
    // 1. 开始播放
    chrome.runtime.sendMessage({ action: 'startReading' }, (response) => {
        console.log("1. 开始播放响应:", response);
        
        setTimeout(() => {
            // 2. 停止播放
            chrome.runtime.sendMessage({ action: 'stopReading' }, (response) => {
                console.log("2. 停止播放响应:", response);
                
                setTimeout(() => {
                    // 3. 再次开始播放
                    chrome.runtime.sendMessage({ action: 'startReading' }, (response) => {
                        console.log("3. 再次播放响应:", response);
                        
                        // 检查最终状态
                        chrome.runtime.sendMessage({ action: 'getState' }, (response) => {
                            console.log("4. 最终状态:", response);
                        });
                    });
                }, 1000);
            });
        }, 2000);
    });
}

// 提供测试函数给控制台
window.testStopPlaySequence = testStopPlaySequence;

console.log("调试脚本加载完成。运行 testStopPlaySequence() 来测试停止播放序列。");
