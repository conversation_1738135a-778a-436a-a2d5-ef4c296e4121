// PDF.js封装器
(function() {
    // 初始化PDF.js配置
    const init = function() {
        try {
            if (typeof pdfjsLib === 'undefined') {
                throw new Error('PDF.js库未加载');
            }
            
            // 配置PDF.js
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('src/lib/pdf.worker.min.js');
                pdfjsLib.PDFJS.workerSrc = chrome.runtime.getURL('src/lib/pdf.worker.min.js');
                pdfjsLib.PDFJS.cMapUrl = chrome.runtime.getURL('src/lib/cmaps/');
                pdfjsLib.PDFJS.cMapPacked = true;
            } else {
                pdfjsLib.GlobalWorkerOptions.workerSrc = 'src/lib/pdf.worker.min.js';
                pdfjsLib.PDFJS.workerSrc = 'src/lib/pdf.worker.min.js';
                pdfjsLib.PDFJS.cMapUrl = 'src/lib/cmaps/';
                pdfjsLib.PDFJS.cMapPacked = true;
            }
            
            console.log('PDF.js配置完成');
            return true;
        } catch (error) {
            console.error('PDF.js初始化失败:', error);
            return false;
        }
    };

    // 加载PDF文档
    const loadPdf = async function(url) {
        try {
            console.log('开始加载PDF:', url);
            const loadingTask = pdfjsLib.getDocument(url);
            const pdf = await loadingTask.promise;
            console.log('PDF加载成功');
            return pdf;
        } catch (error) {
            console.error('PDF加载失败:', error);
            throw error;
        }
    };

    // 获取PDF页面文本
    const getPageText = async function(pdf, pageNumber) {
        try {
            console.log(`开始提取第${pageNumber}页文本`);
            const page = await pdf.getPage(pageNumber);
            const content = await page.getTextContent();
            
            // 提取文本
            let text = '';
            content.items.forEach(item => {
                if (item.str) {
                    text += item.str + ' ';
                }
            });

            console.log('第', pageNumber, '页文本提取完成');
            return text.trim();
        } catch (error) {
            console.error('提取页面文本失败:', error);
            throw error;
        }
    };

    // 处理本地PDF文件
    const handleLocalFile = async function(file) {
        try {
            console.log('处理本地PDF文件:', file.name);
            const fileReader = new FileReader();
            return new Promise((resolve, reject) => {
                fileReader.onload = async function(e) {
                    try {
                        const arrayBuffer = e.target.result;
                        const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
                        const pdf = await loadingTask.promise;
                        resolve(pdf);
                    } catch (error) {
                        console.error('处理本地PDF失败:', error);
                        reject(error);
                    }
                };
                fileReader.onerror = function(error) {
                    console.error('读取文件失败:', error);
                    reject(error);
                };
                fileReader.readAsArrayBuffer(file);
            });
        } catch (error) {
            console.error('处理本地PDF失败:', error);
            throw error;
        }
    };

    // 导出封装器对象
    window.PdfWrapper = {
        init: init,
        loadPdf: loadPdf,
        getPageText: getPageText,
        handleLocalFile: handleLocalFile
    };
})();

// 导出PdfWrapper
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.PdfWrapper;
}
