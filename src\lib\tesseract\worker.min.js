// Tesseract.js worker script
// This file is loaded by Tesseract.js to handle OCR processing in a separate thread

// Initialize Tesseract.js worker
importScripts(chrome.runtime.getURL('src/lib/tesseract/tesseract-core.wasm.js'));

// Set up worker communication
self.onmessage = function(e) {
  const data = e.data;
  if (data.type === 'initialize') {
    // Initialize worker
    Tesseract.workerPath = chrome.runtime.getURL('src/lib/tesseract/worker.min.js');
    Tesseract.corePath = chrome.runtime.getURL('src/lib/tesseract/tesseract-core.wasm.js');
    Tesseract.langPath = chrome.runtime.getURL('src/lib/tesseract/lang');
    self.postMessage({ type: 'initialized' });
  } else if (data.type === 'process') {
    // Process OCR data
    Tesseract.recognize(data.image, 'chi_sim', {
      logger: (m) => console.log(m)
    }).then(({ data: { text } }) => {
      self.postMessage({ type: 'processed', result: text });
    }).catch(err => {
      self.postMessage({ type: 'error', error: err });
    });
  }
};
