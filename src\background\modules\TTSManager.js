// TTS管理模块 - 处理语音合成相关功能
import { BaseBackgroundModule } from './BaseBackgroundModule.js';

export class TTSManager extends BaseBackgroundModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // TTS状态
        this.isTtsAvailable = false;
        this.currentUtterance = null;
        this.currentVoice = null;
        this.currentSpeed = 1.1;
        this.currentPitch = 1.0;
        this.currentVolume = 1.0;
        
        // TTS事件处理
        this.ttsEventHandlers = {
            onstart: null,
            onend: null,
            onerror: null,
            onpause: null,
            onresume: null,
            onboundary: null
        };
        
        // 语音列表
        this.availableVoices = [];
        this.voicesLoaded = false;
        
        // 重试机制
        this.maxRetries = 3;
        this.retryDelay = 1000;

        // 添加原有系统的状态属性
        this.readingState = 'idle';
        this.currentReadingTabId = null;
        this.tabContentCache = {};
    }

    async onInit() {
        // 初始化TTS引擎
        await this.initTTS();
        
        // 设置消息监听器
        this.setupMessageListeners();
        
        // 设置TTS事件监听器
        this.setupTTSEventListeners();
        
        // 加载语音列表
        this.loadVoices();
    }

    // 初始化TTS引擎
    async initTTS() {
        try {
            this.log("初始化TTS引擎...");
            
            // 检查TTS API是否可用
            if (!chrome.tts) {
                throw new Error('TTS API不可用');
            }
            
            // 测试TTS功能
            await this.testTTS();
            
            this.isTtsAvailable = true;
            this.log("✅ TTS引擎初始化成功");
            
        } catch (error) {
            this.error("❌ TTS引擎初始化失败:", error);
            this.isTtsAvailable = false;
            throw error;
        }
    }

    // 测试TTS功能
    async testTTS() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('TTS测试超时'));
            }, 5000);
            
            chrome.tts.speak('', {
                onEvent: (event) => {
                    clearTimeout(timeout);
                    if (event.type === 'start' || event.type === 'end' || event.type === 'error') {
                        resolve();
                    }
                }
            });
        });
    }

    // 设置消息监听器
    setupMessageListeners() {
        // 开始朗读
        this.addMessageListener('startReading', (message, sender, sendResponse) => {
            this.handleStartReading(message, sender, sendResponse);
            return true; // 异步响应
        });

        // 暂停朗读
        this.addMessageListener('pauseReading', (message, sender, sendResponse) => {
            this.handlePauseReading(message, sendResponse);
        });

        // 恢复朗读
        this.addMessageListener('resumeReading', (message, sender, sendResponse) => {
            this.handleResumeReading(message, sendResponse);
        });

        // 停止朗读
        this.addMessageListener('stopReading', (message, sender, sendResponse) => {
            this.handleStopReading(message, sendResponse);
        });

        // 设置语音
        this.addMessageListener('setVoice', (message, sender, sendResponse) => {
            this.handleSetVoice(message, sendResponse);
        });

        // 设置速度
        this.addMessageListener('setSpeed', (message, sender, sendResponse) => {
            this.handleSetSpeed(message, sendResponse);
        });

        // 获取可用语音
        this.addMessageListener('getVoices', (message, sender, sendResponse) => {
            this.handleGetVoices(message, sendResponse);
        });

        // 处理解析内容结果 - 复制原有逻辑
        this.addMessageListener('parsedContent', (message, sender, sendResponse) => {
            this.handleParsedContent(message, sender, sendResponse);
        });

        // TTS状态检查
        this.addMessageListener('checkTTSStatus', (message, sender, sendResponse) => {
            this.handleCheckTTSStatus(message, sendResponse);
        });
    }

    // 设置TTS事件监听器
    setupTTSEventListeners() {
        // 这些事件处理器会在speak时动态设置
        this.ttsEventHandlers = {
            onstart: (event) => {
                this.log("TTS开始播放");
                this.notifyTTSEvent('start', event);
            },
            
            onend: (event) => {
                this.log("TTS播放结束");
                this.currentUtterance = null;
                this.notifyTTSEvent('end', event);
            },
            
            onerror: (event) => {
                this.error("TTS播放错误:", event);
                this.currentUtterance = null;
                this.notifyTTSEvent('error', event);
            },
            
            onpause: (event) => {
                this.log("TTS暂停");
                this.notifyTTSEvent('pause', event);
            },
            
            onresume: (event) => {
                this.log("TTS恢复");
                this.notifyTTSEvent('resume', event);
            },
            
            onboundary: (event) => {
                // 单词边界事件，可用于进度跟踪
                this.notifyTTSEvent('boundary', event);
            }
        };
    }

    // 加载语音列表
    loadVoices() {
        chrome.tts.getVoices((voices) => {
            this.availableVoices = voices || [];
            this.voicesLoaded = true;
            this.log(`已加载 ${this.availableVoices.length} 个语音`);
            
            // 如果没有设置默认语音，选择第一个中文语音
            if (!this.currentVoice && this.availableVoices.length > 0) {
                const chineseVoice = this.availableVoices.find(voice => 
                    voice.lang && voice.lang.startsWith('zh')
                );
                this.currentVoice = chineseVoice || this.availableVoices[0];
                this.log("设置默认语音:", this.currentVoice.name);
            }
        });
    }

    // 处理开始朗读 - 直接复制原有的完整逻辑
    async handleStartReading(message, sender, sendResponse) {
        try {
            this.log("收到startReading请求，当前状态:", this.readingState);
            this.log("currentReadingTabId:", this.currentReadingTabId);
            this.log("sender.tab?.id:", sender.tab?.id);
            this.log("tabContentCache keys:", Object.keys(this.tabContentCache));

            // 检查是否是从暂停状态恢复
            if (this.readingState === 'paused' && this.currentReadingTabId && this.tabContentCache[this.currentReadingTabId]) {
                this.log("🔄 从暂停状态恢复播放");

                // 从暂停位置继续播放
                if (this.currentUtterance && typeof this.currentUtterance.position === 'number') {
                    this.log("从保存位置继续:", this.currentUtterance.position);

                    try {
                        const article = this.tabContentCache[this.currentReadingTabId];

                        if (!article || !article.textContent) {
                            this.error("无法恢复朗读 - 文章内容丢失");
                            this.readingState = 'idle';
                            this.currentUtterance = null;
                            this.sendStateUpdate();
                            sendResponse({ error: "无法恢复朗读 - 文章内容丢失" });
                            return;
                        }

                        // 验证位置有效性
                        if (this.currentUtterance.position < 0 || this.currentUtterance.position >= article.textContent.length) {
                            this.warn("保存的位置无效，从头开始:", this.currentUtterance.position);
                            this.speakText(article.textContent, this.currentReadingTabId);
                        } else {
                            // 有效位置，从保存的位置继续
                            const remainingText = article.textContent.substring(this.currentUtterance.position);
                            this.log(`从位置 ${this.currentUtterance.position} 继续，剩余文本长度: ${remainingText.length}`);
                            this.speakText(remainingText, this.currentReadingTabId, this.currentUtterance.position);
                        }

                        this.readingState = 'reading';
                        this.sendStateUpdate();
                        sendResponse({ status: "从暂停位置恢复播放" });
                        return;

                    } catch (error) {
                        this.error("恢复朗读时出错:", error);
                        // 发生错误时从头开始
                        const article = this.tabContentCache[this.currentReadingTabId];
                        this.speakText(article.textContent, this.currentReadingTabId);
                        this.readingState = 'reading';
                        this.sendStateUpdate();
                        sendResponse({ status: "恢复播放时出错，从头开始" });
                        return;
                    }
                } else {
                    // 如果没有保存位置，则从头开始
                    this.log("无保存位置，从头开始");
                    const article = this.tabContentCache[this.currentReadingTabId];
                    this.speakText(article.textContent, this.currentReadingTabId);
                    this.readingState = 'reading';
                    this.sendStateUpdate();
                    sendResponse({ status: "从头开始播放" });
                    return;
                }
            }

            // 检查是否是从停止状态重新开始播放
            if (this.readingState === 'stopped' && this.currentReadingTabId && this.tabContentCache[this.currentReadingTabId]) {
                this.log("🔄 从停止状态重新开始播放");

                try {
                    const article = this.tabContentCache[this.currentReadingTabId];

                    if (!article || !article.textContent) {
                        this.error("无法重新开始播放 - 文章内容丢失");
                        this.readingState = 'idle';
                        this.sendStateUpdate();
                        sendResponse({ error: "无法重新开始播放 - 文章内容丢失" });
                        return;
                    }

                    // 停止状态重新播放总是从头开始
                    this.log("从头开始播放，文本长度:", article.textContent.length);
                    chrome.tts.stop(); // 确保之前的播放已停止
                    this.speakText(article.textContent, this.currentReadingTabId);
                    this.readingState = 'reading';
                    this.sendStateUpdate();
                    sendResponse({ status: "从停止状态重新开始播放" });
                    return;

                } catch (error) {
                    this.error("从停止状态重新播放时出错:", error);
                    this.readingState = 'idle';
                    this.sendStateUpdate();
                    sendResponse({ error: "重新播放失败: " + error.message });
                    return;
                }
            }

            // 如果没有currentReadingTabId，使用当前活动标签页
            if (!this.currentReadingTabId) {
                const requestTabId = sender.tab?.id;
                if (requestTabId) {
                    this.currentReadingTabId = requestTabId;
                    this.log("设置当前阅读标签页为:", this.currentReadingTabId);
                }
            }

            // 优先使用发送消息的标签页（当前活动标签页）
            const requestTabId = sender.tab?.id;
            const targetTabId = requestTabId || this.currentReadingTabId;

            this.log("目标标签页ID:", targetTabId);
            this.log("是否有缓存:", !!this.tabContentCache[targetTabId]);

            // 检查是否有当前标签页的缓存内容
            if (targetTabId && this.tabContentCache[targetTabId]) {
                const article = this.tabContentCache[targetTabId];
                if (article.textContent) {
                    this.log("使用缓存内容开始播放，标签页:", targetTabId);
                    chrome.tts.stop(); // Stop previous speech first
                    this.log("播放前状态:", this.readingState);

                    // 更新当前阅读标签页
                    this.currentReadingTabId = targetTabId;

                    // 统一使用speakText函数，确保预加载逻辑一致
                    this.speakText(article.textContent, targetTabId);
                    this.readingState = 'reading';
                    this.log("播放后状态:", this.readingState);
                    this.sendStateUpdate();
                    this.log("startReading 完成");
                    sendResponse({ status: "Reading started" });
                } else {
                    this.warn("No text content found in cached article for tab:", targetTabId);
                    this.readingState = 'idle';
                    this.sendStateUpdate();
                    sendResponse({ error: "No text content found" });
                }
            } else {
                this.log("startReading requested but no content cached for target tab:", targetTabId);
                this.log("将像首次进入时一样，主动注入内容脚本并解析页面");

                if (targetTabId) {
                    this.log("开始完整的内容脚本注入和解析流程，标签页:", targetTabId);
                    this.readingState = 'loading';
                    this.currentReadingTabId = targetTabId; // 设置当前阅读标签页ID
                    this.sendStateUpdate();

                    // 使用统一的内容脚本注入和解析函数
                    this.ensureContentParsedAndCached(targetTabId, "startReading")
                        .then(() => {
                            this.log("内容脚本注入完成，等待解析结果");
                            // 解析结果会通过 parsedContent 消息返回，在那里处理播放
                            if (sendResponse) {
                                sendResponse({ status: "正在解析页面内容..." });
                            }
                        })
                        .catch((error) => {
                            this.error("统一的内容脚本注入失败:", error);
                            this.readingState = 'idle';
                            this.sendStateUpdate();
                            if (sendResponse) {
                                sendResponse({ error: `无法解析页面内容: ${error.message}` });
                            }
                        });

                    // 返回 true 表示异步响应
                    return true;
                } else {
                    this.readingState = 'idle';
                    this.sendStateUpdate();
                    sendResponse({ error: "没有可用的页面内容" });
                }
            }

        } catch (error) {
            this.error("开始朗读失败:", error);
            this.readingState = 'idle';
            this.sendStateUpdate();
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理暂停朗读
    handlePauseReading(message, sendResponse) {
        try {
            if (!this.isTtsAvailable) {
                throw new Error('TTS不可用');
            }

            chrome.tts.pause();
            this.log("朗读已暂停");
            sendResponse({ success: true, message: '朗读已暂停' });

        } catch (error) {
            this.error("暂停朗读失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理恢复朗读
    handleResumeReading(message, sendResponse) {
        try {
            if (!this.isTtsAvailable) {
                throw new Error('TTS不可用');
            }

            chrome.tts.resume();
            this.log("朗读已恢复");
            sendResponse({ success: true, message: '朗读已恢复' });

        } catch (error) {
            this.error("恢复朗读失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理停止朗读
    handleStopReading(message, sendResponse) {
        try {
            this.stopSpeaking();
            this.log("朗读已停止");
            sendResponse({ success: true, message: '朗读已停止' });

        } catch (error) {
            this.error("停止朗读失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理设置语音
    handleSetVoice(message, sendResponse) {
        try {
            const { voice } = message;
            
            if (!voice || !voice.name) {
                throw new Error('语音参数无效');
            }

            // 验证语音是否存在
            const foundVoice = this.availableVoices.find(v => v.name === voice.name);
            if (!foundVoice) {
                throw new Error('指定的语音不存在');
            }

            this.currentVoice = foundVoice;
            this.log("语音已设置为:", this.currentVoice.name);
            
            // 保存到存储
            this.setStorage('currentVoice', this.currentVoice);
            
            sendResponse({ success: true, voice: this.currentVoice });

        } catch (error) {
            this.error("设置语音失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理设置速度
    handleSetSpeed(message, sendResponse) {
        try {
            const { speed } = message;
            
            if (typeof speed !== 'number' || speed < 0.1 || speed > 3.0) {
                throw new Error('速度参数无效，应在0.1-3.0之间');
            }

            this.currentSpeed = speed;
            this.log("朗读速度已设置为:", this.currentSpeed);
            
            // 保存到存储
            this.setStorage('currentSpeed', this.currentSpeed);
            
            sendResponse({ success: true, speed: this.currentSpeed });

        } catch (error) {
            this.error("设置速度失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理获取语音列表
    handleGetVoices(message, sendResponse) {
        try {
            if (!this.voicesLoaded) {
                this.loadVoices();
            }

            sendResponse({ 
                success: true, 
                voices: this.availableVoices,
                currentVoice: this.currentVoice
            });

        } catch (error) {
            this.error("获取语音列表失败:", error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理TTS状态检查
    handleCheckTTSStatus(message, sendResponse) {
        chrome.tts.isSpeaking((speaking) => {
            sendResponse({
                success: true,
                status: {
                    available: this.isTtsAvailable,
                    speaking: speaking,
                    currentVoice: this.currentVoice,
                    currentSpeed: this.currentSpeed,
                    voicesCount: this.availableVoices.length
                }
            });
        });
    }

    // 停止朗读
    stopSpeaking() {
        if (this.isTtsAvailable) {
            // 多次调用确保停止
            for (let i = 0; i < 3; i++) {
                chrome.tts.stop();
            }
            this.currentUtterance = null;
        }
    }

    // 检查是否正在朗读
    isSpeaking() {
        return new Promise((resolve) => {
            if (!this.isTtsAvailable) {
                resolve(false);
                return;
            }
            
            chrome.tts.isSpeaking(resolve);
        });
    }

    // 通知TTS事件
    notifyTTSEvent(eventType, eventData) {
        // 通知其他模块TTS事件
        if (this.dependencies.moduleContainer) {
            this.dependencies.moduleContainer.emit('ttsEvent', {
                type: eventType,
                data: eventData,
                timestamp: Date.now()
            });
        }

        // 发送到前端
        this.sendMessageToUI({
            action: 'ttsEvent',
            eventType: eventType,
            eventData: eventData
        });
    }

    // 获取TTS状态
    getStatus() {
        return {
            ...super.getStatus(),
            isTtsAvailable: this.isTtsAvailable,
            currentVoice: this.currentVoice?.name,
            currentSpeed: this.currentSpeed,
            voicesCount: this.availableVoices.length,
            isPlaying: !!this.currentUtterance
        };
    }

    // 统一的内容脚本注入和解析函数 - 直接复制原有逻辑
    async ensureContentParsedAndCached(tabId, reason = "unknown") {
        return new Promise((resolve, reject) => {
            this.log(`开始确保内容解析和缓存 (${reason})，标签页: ${tabId}`);

            // 检查是否已经有缓存
            if (this.tabContentCache[tabId]) {
                this.log(`已有缓存内容 (${reason})，直接使用`);
                resolve(this.tabContentCache[tabId]);
                return;
            }

            // 设置解析超时
            const parseTimeout = setTimeout(() => {
                this.warn(`内容解析超时 (${reason})`);
                reject(new Error("内容解析超时"));
            }, 15000);

            try {
                this.log(`直接发送解析请求到内容脚本 (${reason}) - 避免重复注入`);

                // 直接发送解析请求，不重复注入脚本
                // 因为在manifest.json中已经配置了自动注入
                chrome.tabs.sendMessage(tabId, { action: 'parseContent' }, (response) => {
                    if (chrome.runtime.lastError) {
                        this.error(`解析请求失败 (${reason}):`, chrome.runtime.lastError.message);

                        // 如果直接发送失败，可能是内容脚本未加载，尝试注入
                        this.log(`尝试注入内容脚本后重试 (${reason})`);

                        chrome.scripting.executeScript({
                            target: { tabId: tabId },
                            files: ["src/lib/Readability.js", "src/content/parser.js"]
                        }).then((injectionResult) => {
                            this.log(`内容脚本注入完成 (${reason})`);

                            // 给内容脚本时间初始化，然后发送解析请求
                            setTimeout(() => {
                                this.log(`发送解析请求到内容脚本 (${reason})`);

                                chrome.tabs.sendMessage(tabId, { action: 'parseContent' }, (response) => {
                                    clearTimeout(parseTimeout);

                                    if (chrome.runtime.lastError) {
                                        this.error(`注入后解析请求仍失败 (${reason}):`, chrome.runtime.lastError.message);
                                        reject(new Error(`解析请求失败: ${chrome.runtime.lastError.message}`));
                                    } else {
                                        this.log(`解析请求成功发送 (${reason})，等待解析结果`);
                                        // 解析结果会通过现有的 parsedContent 消息处理器处理
                                        resolve("parsing_started");
                                    }
                                });
                            }, 800); // 给内容脚本800ms时间初始化

                        }).catch((error) => {
                            this.error(`注入内容脚本失败 (${reason}):`, error);
                            clearTimeout(parseTimeout);
                            reject(new Error(`注入内容脚本失败: ${error.message}`));
                        });
                    } else {
                        clearTimeout(parseTimeout);
                        this.log(`解析请求成功发送 (${reason})，等待解析结果`);
                        // 解析结果会通过现有的 parsedContent 消息处理器处理
                        resolve("parsing_started");
                    }
                });

            } catch (error) {
                this.error(`执行内容脚本注入时出错 (${reason}):`, error);
                clearTimeout(parseTimeout);
                reject(error);
            }
        });
    }

    // 发送状态更新 - 复制原有逻辑
    sendStateUpdate() {
        const stateManager = this.dependencies.moduleContainer?.getModule('StateManager');
        if (stateManager) {
            stateManager.updateState({
                readingState: this.readingState,
                currentReadingTabId: this.currentReadingTabId,
                currentSpeed: this.currentSpeed,
                currentVoice: this.currentVoice
            }, 'tts_update');
        }
    }

    // 朗读文本 - 复制原有逻辑
    speakText(text, tabId, startPosition = 0) {
        if (!text || text.trim() === '') {
            this.error("朗读文本为空");
            return;
        }

        this.log("开始朗读文本，长度:", text.length, "起始位置:", startPosition);

        // 准备TTS选项
        const ttsOptions = {
            voiceName: this.currentVoice?.name,
            rate: this.currentSpeed,
            pitch: this.currentPitch,
            volume: this.currentVolume,
            onstart: () => {
                this.log("TTS开始播放");
                this.readingState = 'reading';
                this.sendStateUpdate();
            },
            onend: () => {
                this.log("TTS播放结束");
                this.readingState = 'idle';
                this.currentUtterance = null;
                this.sendStateUpdate();
            },
            onerror: (event) => {
                this.error("TTS播放错误:", event);
                this.readingState = 'error';
                this.sendStateUpdate();
            },
            onpause: () => {
                this.log("TTS暂停");
                this.readingState = 'paused';
                this.sendStateUpdate();
            },
            onresume: () => {
                this.log("TTS恢复");
                this.readingState = 'reading';
                this.sendStateUpdate();
            }
        };

        // 保存当前播放信息
        this.currentUtterance = {
            text: text,
            position: startPosition,
            tabId: tabId
        };

        // 开始朗读
        chrome.tts.speak(text, ttsOptions);
    }

    // 处理解析内容结果 - 直接复制原有逻辑
    handleParsedContent(message, sender, sendResponse) {
        const tabId = sender.tab?.id;

        if (tabId && message.article) {
            this.log(`收到页面解析结果 - 标签页 ${tabId}`);
            this.log(`页面标题: ${message.article.title}`);
            this.log(`内容长度: ${message.article.textContent?.length || 0}`);
            this.log(`页面URL: ${message.article.url || '未知'}`);

            // 保存解析的文章内容到内存缓存
            this.tabContentCache[tabId] = message.article;
            this.log(`已更新标签页 ${tabId} 的内容缓存`);

            // 检查是否是由播放按钮触发的解析（状态为loading）
            if (this.readingState === 'loading') {
                this.log("检测到播放按钮触发的解析，自动开始播放");
                this.readingState = 'reading';
                this.currentReadingTabId = tabId;
                this.speakText(message.article.textContent, tabId);
                this.sendStateUpdate();
                if (sendResponse) {
                    sendResponse({ status: "解析完成，开始播放" });
                }
            } else if (this.readingState === 'navigating') {
                this.log("检测到导航状态的解析，自动开始播放下一章");
                this.readingState = 'reading';
                this.currentReadingTabId = tabId;
                this.speakText(message.article.textContent, tabId);
                this.sendStateUpdate();
                if (sendResponse) {
                    sendResponse({ status: "导航完成，开始播放下一章" });
                }
            } else if (this.readingState === 'reading' || this.readingState === 'paused') {
                this.log(`已有朗读在进行(${this.readingState})，不改变状态`);
                // 仅缓存内容，不更改状态或发送更新
                if (sendResponse) {
                    sendResponse({ status: "内容已缓存，现有朗读不受影响" });
                }
            } else {
                // 普通解析，设置为idle状态
                this.readingState = 'idle';
                this.currentReadingTabId = tabId;
                this.sendStateUpdate();
                sendResponse({ status: "Parsed content received and cached" });
            }

            return true; // 异步响应
        } else {
            this.error("Invalid parsedContent message", message);
            sendResponse({ error: "Invalid parsedContent message" });
        }
    }

    // 公共API
    async speak(text, options = {}) {
        return new Promise((resolve, reject) => {
            this.handleStartReading({ text, options }, null, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    async pause() {
        return new Promise((resolve, reject) => {
            this.handlePauseReading({}, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    async resume() {
        return new Promise((resolve, reject) => {
            this.handleResumeReading({}, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    async stop() {
        return new Promise((resolve, reject) => {
            this.handleStopReading({}, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    setVoice(voice) {
        return new Promise((resolve, reject) => {
            this.handleSetVoice({ voice }, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    setSpeed(speed) {
        return new Promise((resolve, reject) => {
            this.handleSetSpeed({ speed }, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    getVoices() {
        return new Promise((resolve, reject) => {
            this.handleGetVoices({}, (response) => {
                if (response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }
}
