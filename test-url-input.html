<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL输入框清除按钮测试</title>
    <link rel="stylesheet" href="src/ui/sidepanel/url-input-styles.css">
    <style>
        body {
            background: #1a1a2e;
            color: #F0F0F5;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        .test-item {
            margin: 20px 0;
        }
        .test-label {
            font-size: 14px;
            margin-bottom: 10px;
            color: #F0F0F5;
        }
        .demo-section {
            margin: 30px 0;
            padding: 15px;
            border: 1px dashed rgba(255, 255, 255, 0.2);
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>URL输入框清除按钮测试</h2>
        
        <div class="demo-section">
            <div class="test-label">URL输入框（带清除按钮）：</div>
            <div class="url-input-container">
                <div class="url-input-wrapper">
                    <input type="url" id="smart-url-input" placeholder="输入网址播放指定内容，留空播放当前页面" class="smart-url-input" title="输入网址或留空播放当前页面">
                    <button id="clear-url-btn" class="clear-url-btn" title="清除输入" style="display: none;">🧹</button>
                </div>
                <div id="url-validation-message" class="url-validation-message" style="display: none;"></div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="test-label">测试步骤：</div>
            <ol style="font-size: 12px; color: #A09CB0; line-height: 1.6;">
                <li>在输入框中输入任意文字</li>
                <li>观察右侧是否出现清除按钮（×）</li>
                <li>点击清除按钮测试清除功能</li>
                <li>输入有效URL测试验证功能</li>
                <li>输入无效URL测试错误提示</li>
            </ol>
        </div>
        
        <div class="demo-section">
            <div class="test-label">功能特点：</div>
            <ul style="font-size: 12px; color: #A09CB0; line-height: 1.6;">
                <li>✅ 输入内容时自动显示清除按钮</li>
                <li>✅ 清空内容时自动隐藏清除按钮</li>
                <li>✅ 扫把图标，无背景框设计</li>
                <li>✅ 清除按钮hover效果（红色放大）</li>
                <li>✅ 点击清除后自动聚焦输入框</li>
                <li>✅ 清除时同时清除验证消息</li>
                <li>✅ 播放状态下输入框被锁定</li>
                <li>✅ 停止按钮清除所有相关状态</li>
            </ul>
        </div>

        <div class="demo-section">
            <div class="test-label">播放状态模拟测试：</div>
            <div style="margin: 10px 0;">
                <button onclick="simulatePlayingState()" style="padding: 8px 16px; margin-right: 10px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">模拟播放状态</button>
                <button onclick="simulateStoppedState()" style="padding: 8px 16px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">模拟停止状态</button>
            </div>
            <div style="font-size: 12px; color: #A09CB0; margin-top: 10px;">
                点击按钮测试输入框在不同状态下的表现
            </div>
        </div>

        <p style="margin-top: 30px; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
            <strong>设计规格：</strong><br>
            • 清除按钮：20px × 20px，扫把图标🧹<br>
            • 位置：输入框右侧内部，距离边缘8px<br>
            • 背景：完全透明，无背景框<br>
            • 颜色：默认灰色，hover时红色<br>
            • 动画：hover时放大1.2倍，点击时恢复原大小<br>
            • 显示逻辑：有内容时显示，无内容时隐藏<br><br>
            <strong>播放状态控制：</strong><br>
            • 播放中：输入框锁定，提示"需要停止播放后才能输入网址"<br>
            • 暂停中：输入框锁定，提示"需要停止播放后才能输入网址"<br>
            • 停止后：自动清除输入框并解锁，恢复原始提示<br>
            • 状态同步：前后端缓存一致性管理
        </p>
    </div>
    
    <script>
        // URL验证函数
        function isValidURL(string) {
            try {
                const url = new URL(string);
                return url.protocol === 'http:' || url.protocol === 'https:';
            } catch (_) {
                return false;
            }
        }

        // 显示URL验证消息
        function showURLValidationMessage(message, isError = false) {
            const urlValidationMessage = document.getElementById('url-validation-message');
            if (!urlValidationMessage) return;

            urlValidationMessage.textContent = message;
            urlValidationMessage.className = `url-validation-message ${isError ? 'error' : 'success'}`;
            urlValidationMessage.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                if (urlValidationMessage) {
                    urlValidationMessage.style.display = 'none';
                }
            }, 3000);
        }

        // 初始化功能
        document.addEventListener('DOMContentLoaded', function() {
            const smartUrlInput = document.getElementById('smart-url-input');
            const clearUrlBtn = document.getElementById('clear-url-btn');
            const urlValidationMessage = document.getElementById('url-validation-message');

            // 智能URL输入框事件
            if (smartUrlInput) {
                // 实时验证URL和控制清除按钮显示
                smartUrlInput.addEventListener('input', function() {
                    const url = this.value.trim();
                    
                    // 控制清除按钮的显示/隐藏
                    if (clearUrlBtn) {
                        clearUrlBtn.style.display = url ? 'flex' : 'none';
                    }
                    
                    // URL验证
                    if (url && !isValidURL(url)) {
                        this.style.borderColor = '#e74c3c';
                        showURLValidationMessage("请输入有效的网址格式", true);
                    } else {
                        this.style.borderColor = '';
                        if (urlValidationMessage) {
                            urlValidationMessage.style.display = 'none';
                        }
                    }
                });
            }

            // 清除URL按钮事件
            if (clearUrlBtn) {
                clearUrlBtn.addEventListener('click', function() {
                    if (smartUrlInput) {
                        smartUrlInput.value = '';
                        smartUrlInput.focus();
                        // 隐藏清除按钮
                        this.style.display = 'none';
                        // 隐藏验证消息
                        if (urlValidationMessage) {
                            urlValidationMessage.style.display = 'none';
                        }
                        // 重置边框颜色
                        smartUrlInput.style.borderColor = '';
                    }
                });
            }
        });

        // 播放状态模拟测试
        function simulatePlayingState() {
            const smartUrlInput = document.getElementById('smart-url-input');
            if (smartUrlInput) {
                smartUrlInput.disabled = true;
                smartUrlInput.style.opacity = '0.5';
                smartUrlInput.style.cursor = 'not-allowed';
                smartUrlInput.placeholder = '需要停止播放后才能输入网址';
            }
        }

        function simulateStoppedState() {
            const smartUrlInput = document.getElementById('smart-url-input');
            const clearUrlBtn = document.getElementById('clear-url-btn');
            if (smartUrlInput) {
                smartUrlInput.value = '';
                smartUrlInput.disabled = false;
                smartUrlInput.style.opacity = '1';
                smartUrlInput.style.cursor = 'text';
                smartUrlInput.placeholder = '输入网址播放指定内容，留空播放当前页面';
            }
            if (clearUrlBtn) {
                clearUrlBtn.style.display = 'none';
            }
        }
    </script>
</body>
</html>
