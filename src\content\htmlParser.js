// HTML内容解析模块
console.log("🔧 HTML解析器已加载");

/**
 * 从HTML页面提取主要内容
 * @returns {Object} 包含标题和正文内容的对象
 */
export function extractHtmlContent() {
  console.log("📄 开始提取HTML内容");

  try {
    // 检查Readability库是否可用
    if (typeof Readability === 'undefined') {
      console.error("❌ Readability库未加载");
      return { title: document.title, content: getFallbackContent() };
    }

    // 克隆文档以避免修改原始DOM
    const documentClone = document.cloneNode(true);

    // 使用Readability解析主要内容
    const reader = new Readability(documentClone, {
      classesToPreserve: ['article', 'content', 'main', 'text', 'story']
    });
    const article = reader.parse();

    if (article && article.textContent) {
      console.log("✅ HTML内容提取成功");
      return {
        title: article.title || document.title,
        content: article.textContent,
        url: window.location.href
      };
    } else {
      console.warn("⚠️ Readability未能提取内容，使用备用方案");
      return { title: document.title, content: getFallbackContent() };
    }
  } catch (error) {
    console.error("❌ HTML内容提取失败:", error);
    return { title: document.title, content: getFallbackContent() };
  }
}

/**
 * 备用内容提取方案 - 当Readability失败时使用
 * @returns {string} 提取的文本内容
 */
function getFallbackContent() {
  // 尝试常见的文章容器选择器
  const contentSelectors = [
    'article', 'main', '.post-content', '.entry-content', '#content',
    '.article-body', '.story-content', '.post-body', '.blog-post'
  ];

  for (const selector of contentSelectors) {
    const element = document.querySelector(selector);
    if (element) {
      return element.innerText.trim();
    }
  }

  // 如果没有找到特定容器，使用body文本（过滤掉脚本和样式）
  const body = document.body;
  const clone = body.cloneNode(true);
  const scripts = clone.querySelectorAll('script, style, noscript, iframe, svg');
  scripts.forEach(el => el.remove());
  return clone.innerText.trim();
}

/**
 * 查找下一页链接
 * @returns {string|null} 下一页URL或null
 */
export function findNextPageLink() {
  console.log("🔍 查找下一页链接");

  const keywords = [
    '下一页', '下一章', 'next page', 'next chapter',
    '下页', '下章', '下一节', 'next section', '继续阅读'
  ];

  const links = Array.from(document.querySelectorAll('a[href]'));

  for (const keyword of keywords) {
    const lowerKeyword = keyword.toLowerCase();
    for (const link of links) {
      const linkText = link.textContent.trim().toLowerCase();
      const linkHref = link.href.toLowerCase();

      if (linkText.includes(lowerKeyword) || linkHref.includes(lowerKeyword)) {
        // 排除上一页和返回链接
        if (!linkText.includes('上一') && !linkText.includes('previous') &&
            !linkText.includes('返回') && !linkText.includes('back')) {
          console.log(`✅ 找到下一页链接: ${link.href}`);
          return link.href;
        }
      }
    }
  }

  console.log("❌ 未找到下一页链接");
  return null;
}

// 暴露到全局作用域供内容脚本使用
window.htmlParser = {
  extractHtmlContent,
  findNextPageLink
};