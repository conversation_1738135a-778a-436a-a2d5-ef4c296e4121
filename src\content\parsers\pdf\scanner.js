export class PdfScanner {
    constructor() {
        this.overlay = null;
        this.scanLine = null;
        this.pageIndicator = null;
        this.progressBar = null;
        this.isActive = false;
    }

    init() {
        if (this.overlay) return;

        this.overlay = document.createElement('div');
        this.overlay.className = 'shenlamp-pdf-scanner-overlay';
        this.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2147483647;
            pointer-events: none;
            overflow: hidden;
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .shenlamp-pdf-scanner-overlay .scan-line {
                position: absolute;
                left: 0;
                width: 100%;
                height: 4px;
                background: linear-gradient(to right, 
                    rgba(0, 255, 0, 0), 
                    rgba(0, 221, 85, 0.8),
                    rgba(0, 255, 0, 0));
                box-shadow: 0 0 15px 2px rgba(0, 221, 85, 0.6);
                transform-origin: 0 50%;
                z-index: 10000;
                pointer-events: none;
            }
            
            @keyframes shenlamp-scan {
                0% { transform: translateY(0); opacity: 0.8; }
                100% { transform: translateY(100vh); opacity: 0; }
            }
            
            .shenlamp-pdf-scanner-overlay .page-indicator {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.7);
                color: #0f0;
                padding: 8px 16px;
                border-radius: 4px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                font-size: 14px;
                z-index: 10001;
                pointer-events: none;
                backdrop-filter: blur(5px);
                border: 1px solid rgba(0, 221, 85, 0.3);
            }
            
            .shenlamp-pdf-scanner-overlay .progress-bar {
                position: fixed;
                top: 0;
                left: 0;
                width: 0;
                height: 3px;
                background: #0f0;
                box-shadow: 0 0 10px #0f0;
                z-index: 10002;
                transition: width 0.3s ease;
            }
            
            .shenlamp-pdf-scanner-overlay .highlight-area {
                position: absolute;
                background: rgba(0, 221, 85, 0.1);
                border: 1px dashed rgba(0, 221, 85, 0.6);
                pointer-events: none;
                z-index: 9998;
                transition: all 0.3s ease;
            }
            
            .shenlamp-pdf-scanner-overlay .highlight-line {
                position: absolute;
                left: 0;
                width: 100%;
                height: 2px;
                background: rgba(0, 221, 85, 0.7);
                box-shadow: 0 0 10px rgba(0, 221, 85, 0.7);
                animation: shenlamp-scan 1.5s infinite;
            }
        `;
        document.head.appendChild(style);

        // 创建扫描线
        this.scanLine = document.createElement('div');
        this.scanLine.className = 'scan-line';
        this.overlay.appendChild(this.scanLine);

        // 创建页面指示器
        this.pageIndicator = document.createElement('div');
        this.pageIndicator.className = 'page-indicator';
        this.overlay.appendChild(this.pageIndicator);

        // 创建进度条
        this.progressBar = document.createElement('div');
        this.progressBar.className = 'progress-bar';
        this.overlay.appendChild(this.progressBar);

        document.body.appendChild(this.overlay);
    }

    start(initialPage = 1, totalPages = 1) {
        if (!this.overlay) this.init();
        
        this.currentPage = initialPage;
        this.totalPages = totalPages;
        this.isActive = true;
        
        this.updatePageIndicator();
        this.animateScanLine();
    }

    updatePageInfo(pageNum, totalPages) {
        this.currentPage = pageNum;
        this.totalPages = totalPages;
        this.updatePageIndicator();
    }

    updatePageIndicator() {
        if (!this.pageIndicator) return;
        this.pageIndicator.textContent = `处理中: 第 ${this.currentPage} 页 / 共 ${this.totalPages} 页`;
    }

    updateProgress(percent) {
        if (!this.progressBar) return;
        this.progressBar.style.width = `${percent}%`;
    }

    highlightArea(rect) {
        if (!this.overlay) return;
        
        // 移除旧的高亮
        const oldHighlight = this.overlay.querySelector('.highlight-area');
        if (oldHighlight) {
            oldHighlight.remove();
        }
        
        if (!rect) return;
        
        // 创建新的高亮区域
        const highlight = document.createElement('div');
        highlight.className = 'highlight-area';
        highlight.style.left = `${rect.left}px`;
        highlight.style.top = `${rect.top}px`;
        highlight.style.width = `${rect.width}px`;
        highlight.style.height = `${rect.height}px`;
        
        // 添加扫描线
        const scanLine = document.createElement('div');
        scanLine.className = 'highlight-line';
        highlight.appendChild(scanLine);
        
        this.overlay.appendChild(highlight);
    }

    animateScanLine() {
        if (!this.isActive || !this.scanLine) return;

        // 重置位置
        this.scanLine.style.top = '0';
        this.scanLine.style.transition = 'none';
        this.scanLine.style.opacity = '0.8';
        
        // 强制重绘
        void this.scanLine.offsetWidth;
        
        // 开始动画
        this.scanLine.style.transition = 'top 2s linear, opacity 0.5s ease-out';
        this.scanLine.style.top = '100%';
        this.scanLine.style.opacity = '0.4';
        
        // 动画结束回调
        const onAnimationEnd = () => {
            if (this.isActive) {
                this.animateScanLine();
            }
        };
        
        this.scanLine.removeEventListener('transitionend', onAnimationEnd);
        this.scanLine.addEventListener('transitionend', onAnimationEnd, { once: true });
    }

    stop() {
        this.isActive = false;
        if (this.overlay && this.overlay.parentNode) {
            document.body.removeChild(this.overlay);
            this.overlay = null;
        }
    }
}
