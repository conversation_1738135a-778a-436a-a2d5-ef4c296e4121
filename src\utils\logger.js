// 生产环境日志管理器
// 用于替换所有console.log调试语句

class Logger {
    constructor() {
        // 从manifest获取版本信息判断是否为开发环境
        this.isDevelopment = this.checkDevelopmentMode();
        this.logLevel = this.isDevelopment ? 'debug' : 'error';
    }

    checkDevelopmentMode() {
        try {
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
                const manifest = chrome.runtime.getManifest();
                // 如果版本包含dev、beta或小于1.0.0则认为是开发环境
                return manifest.version.includes('dev') || 
                       manifest.version.includes('beta') || 
                       parseFloat(manifest.version) < 1.0;
            }
        } catch (error) {
            // 如果无法获取manifest，默认为生产环境
        }
        return false;
    }

    // 调试日志 - 仅开发环境输出
    debug(...args) {
        if (this.isDevelopment && this.logLevel === 'debug') {
            console.log('[DEBUG]', ...args);
        }
    }

    // 信息日志 - 开发环境输出
    info(...args) {
        if (this.isDevelopment) {
            console.info('[INFO]', ...args);
        }
    }

    // 警告日志 - 始终输出
    warn(...args) {
        console.warn('[WARN]', ...args);
    }

    // 错误日志 - 始终输出
    error(...args) {
        console.error('[ERROR]', ...args);
    }

    // 性能日志 - 仅开发环境
    perf(label, fn) {
        if (this.isDevelopment) {
            console.time(label);
            const result = fn();
            console.timeEnd(label);
            return result;
        }
        return fn();
    }

    // 异步性能日志
    async perfAsync(label, fn) {
        if (this.isDevelopment) {
            console.time(label);
            const result = await fn();
            console.timeEnd(label);
            return result;
        }
        return await fn();
    }
}

// 创建全局日志实例
const logger = new Logger();

// 导出便捷函数
export const debug = (...args) => logger.debug(...args);
export const info = (...args) => logger.info(...args);
export const warn = (...args) => logger.warn(...args);
export const error = (...args) => logger.error(...args);
export const perf = (label, fn) => logger.perf(label, fn);
export const perfAsync = (label, fn) => logger.perfAsync(label, fn);

export default logger;
