// 语音管理模块 - 处理语音选择、收藏、过滤等功能
import { BaseModule } from './BaseModule.js';

export class VoiceManager extends BaseModule {
    constructor(name, dependencies) {
        super(name, dependencies);
        
        // 语音相关状态
        this.availableVoices = [];
        this.favoriteVoices = [];
        this.currentVoice = null;
        this.voiceFilters = {
            gender: [],
            language: 'all'
        };
        
        // DOM元素引用
        this.voiceSelect = null;
        this.favoriteVoicesList = null;
        this.availableVoicesList = null;
        this.voiceSearchInput = null;
        this.defaultVoiceSelect = null;
        this.voiceFilterTabs = null;
        this.genderCheckboxes = null;
    }

    async onInit() {
        // 获取DOM元素
        this.initDOMElements();
        
        // 加载收藏语音
        await this.loadFavoriteVoices();
        
        // 加载默认语音
        await this.loadDefaultVoice();
        
        // 设置事件监听器
        this.setupEventListeners();
        
        // 初始化语音列表
        this.initializeVoiceList();
    }

    initDOMElements() {
        this.voiceSelect = document.getElementById('voice-select');
        this.favoriteVoicesList = document.getElementById('favorite-voices-list');
        this.availableVoicesList = document.getElementById('available-voices-list');
        this.voiceSearchInput = document.getElementById('voice-search-input');
        this.defaultVoiceSelect = document.getElementById('default-voice-select');
        this.voiceFilterTabs = document.querySelectorAll('.voice-filter-tab');
        this.genderCheckboxes = document.querySelectorAll('.gender-checkbox');
    }

    setupEventListeners() {
        // 语音选择变化
        if (this.voiceSelect) {
            this.addEventListener(this.voiceSelect, 'change', (e) => {
                this.handleVoiceChange(e.target.value);
            });
        }

        // 语音搜索
        if (this.voiceSearchInput) {
            this.addEventListener(this.voiceSearchInput, 'input', (e) => {
                this.handleVoiceSearch(e.target.value);
            });
        }

        // 语音过滤标签
        this.voiceFilterTabs.forEach(tab => {
            this.addEventListener(tab, 'click', (e) => {
                this.handleFilterTabClick(e.target);
            });
        });

        // 性别过滤复选框
        this.genderCheckboxes.forEach(checkbox => {
            this.addEventListener(checkbox, 'change', (e) => {
                this.handleGenderFilterChange();
            });
        });
    }

    // 加载收藏语音
    async loadFavoriteVoices() {
        try {
            const result = await this.dependencies.chrome.storage.local.get(['favoriteVoices']);
            this.favoriteVoices = result.favoriteVoices || [];
            console.log('已加载收藏语音:', this.favoriteVoices);
        } catch (error) {
            this.errorHandler.handleError(error, 'load_favorite_voices');
            this.favoriteVoices = [];
        }
    }

    // 保存收藏语音
    async saveFavoriteVoices() {
        try {
            await this.dependencies.chrome.storage.local.set({
                favoriteVoices: this.favoriteVoices
            });
            console.log('收藏语音已保存');
        } catch (error) {
            this.errorHandler.handleError(error, 'save_favorite_voices');
        }
    }

    // 添加语音到收藏
    addVoiceToFavorites(voiceName) {
        if (!this.favoriteVoices.includes(voiceName)) {
            this.favoriteVoices.push(voiceName);
            this.saveFavoriteVoices();
            this.renderFavoriteVoices();
            this.renderAvailableVoices();
        }
    }

    // 从收藏中移除语音
    removeVoiceFromFavorites(voiceName) {
        const index = this.favoriteVoices.indexOf(voiceName);
        if (index > -1) {
            this.favoriteVoices.splice(index, 1);
            this.saveFavoriteVoices();
            this.renderFavoriteVoices();
            this.renderAvailableVoices();
        }
    }

    // 检测语言类别
    detectLanguageCategory(langCode) {
        if (!langCode) return 'other';
        
        const lang = langCode.toLowerCase();
        if (lang.startsWith('zh')) return 'chinese';
        if (lang.startsWith('en')) return 'english';
        if (lang.startsWith('ja')) return 'japanese';
        if (lang.startsWith('ko')) return 'korean';
        if (['fr', 'de', 'es', 'it', 'pt', 'ru', 'ar'].some(l => lang.startsWith(l))) {
            return 'other';
        }
        return 'other';
    }

    // 渲染收藏语音列表
    renderFavoriteVoices() {
        if (!this.favoriteVoicesList) return;

        this.favoriteVoicesList.innerHTML = '';

        if (this.favoriteVoices.length === 0) {
            this.favoriteVoicesList.innerHTML = `
                <div class="empty-state">
                    <p>暂无收藏语音</p>
                    <small>在可用语音列表中点击 ⭐ 添加收藏</small>
                </div>
            `;
            return;
        }

        this.favoriteVoices.forEach(voiceName => {
            const voice = this.availableVoices.find(v => v.name === voiceName);
            if (voice) {
                const voiceElement = this.createVoiceElement(voice, true);
                this.favoriteVoicesList.appendChild(voiceElement);
            }
        });
    }

    // 渲染可用语音列表
    renderAvailableVoices() {
        if (!this.availableVoicesList) return;

        // 应用过滤器
        const filteredVoices = this.filterVoices(this.availableVoices);
        
        this.availableVoicesList.innerHTML = '';

        if (filteredVoices.length === 0) {
            this.availableVoicesList.innerHTML = `
                <div class="empty-state">
                    <p>没有找到匹配的语音</p>
                    <small>尝试调整过滤条件</small>
                </div>
            `;
            return;
        }

        filteredVoices.forEach(voice => {
            const voiceElement = this.createVoiceElement(voice, false);
            this.availableVoicesList.appendChild(voiceElement);
        });
    }

    // 创建语音元素
    createVoiceElement(voice, isFavorite) {
        const voiceItem = document.createElement('div');
        voiceItem.className = 'voice-item';
        voiceItem.dataset.voiceName = voice.name;

        const langName = this.getLangName(voice.lang);
        const isCurrentVoice = this.currentVoice && this.currentVoice.name === voice.name;

        voiceItem.innerHTML = `
            <div class="voice-info">
                <div class="voice-name ${isCurrentVoice ? 'current-voice' : ''}">${voice.name}</div>
                <div class="voice-details">
                    <span class="voice-lang">${langName}</span>
                    ${voice.gender ? `<span class="voice-gender">${voice.gender}</span>` : ''}
                    ${voice.quality ? `<span class="voice-quality">${voice.quality}</span>` : ''}
                </div>
            </div>
            <div class="voice-actions">
                <button class="preview-btn" title="试听语音">▶</button>
                <button class="favorite-btn ${isFavorite ? 'favorited' : ''}" 
                        title="${isFavorite ? '取消收藏' : '添加收藏'}">
                    ${isFavorite ? '★' : '☆'}
                </button>
                <button class="select-btn" title="选择此语音">选择</button>
            </div>
        `;

        // 添加事件监听器
        const previewBtn = voiceItem.querySelector('.preview-btn');
        const favoriteBtn = voiceItem.querySelector('.favorite-btn');
        const selectBtn = voiceItem.querySelector('.select-btn');

        previewBtn.addEventListener('click', () => this.previewVoice(voice));
        favoriteBtn.addEventListener('click', () => this.toggleVoiceFavorite(voice.name));
        selectBtn.addEventListener('click', () => this.selectVoice(voice));

        return voiceItem;
    }

    // 过滤语音
    filterVoices(voices) {
        return voices.filter(voice => {
            // 搜索过滤
            if (this.voiceSearchInput && this.voiceSearchInput.value) {
                const searchTerm = this.voiceSearchInput.value.toLowerCase();
                if (!voice.name.toLowerCase().includes(searchTerm) &&
                    !voice.lang.toLowerCase().includes(searchTerm)) {
                    return false;
                }
            }

            // 语言过滤
            if (this.voiceFilters.language !== 'all') {
                const category = this.detectLanguageCategory(voice.lang);
                if (category !== this.voiceFilters.language) {
                    return false;
                }
            }

            // 性别过滤
            if (this.voiceFilters.gender.length > 0) {
                if (!this.voiceFilters.gender.includes(voice.gender)) {
                    return false;
                }
            }

            return true;
        });
    }

    // 获取语言名称
    getLangName(langCode) {
        const langMap = {
            'zh-CN': '中文(简体)',
            'zh-TW': '中文(繁体)',
            'zh-HK': '中文(香港)',
            'en-US': 'English (US)',
            'en-GB': 'English (UK)',
            'ja-JP': '日本語',
            'ko-KR': '한국어',
            'fr-FR': 'Français',
            'de-DE': 'Deutsch',
            'es-ES': 'Español',
            'it-IT': 'Italiano',
            'pt-BR': 'Português',
            'ru-RU': 'Русский',
            'ar-SA': 'العربية'
        };
        return langMap[langCode] || langCode || '未知';
    }

    // 处理语音变化
    handleVoiceChange(voiceName) {
        const voice = this.availableVoices.find(v => v.name === voiceName);
        if (voice) {
            this.selectVoice(voice);
        }
    }

    // 处理语音搜索
    handleVoiceSearch(searchTerm) {
        this.renderAvailableVoices();
    }

    // 处理过滤标签点击
    handleFilterTabClick(tab) {
        // 移除其他标签的active状态
        this.voiceFilterTabs.forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        
        this.voiceFilters.language = tab.dataset.filter;
        this.renderAvailableVoices();
    }

    // 处理性别过滤变化
    handleGenderFilterChange() {
        this.voiceFilters.gender = Array.from(this.genderCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);
        this.renderAvailableVoices();
    }

    // 预览语音
    previewVoice(voice) {
        const testText = "这是语音预览测试。";
        this.dependencies.chrome.tts.speak(testText, {
            voiceName: voice.name,
            rate: 1.0,
            pitch: 1.0,
            volume: 1.0
        });
    }

    // 切换语音收藏状态
    toggleVoiceFavorite(voiceName) {
        if (this.favoriteVoices.includes(voiceName)) {
            this.removeVoiceFromFavorites(voiceName);
        } else {
            this.addVoiceToFavorites(voiceName);
        }
    }

    // 选择语音
    selectVoice(voice) {
        this.currentVoice = voice;
        
        // 更新UI
        if (this.voiceSelect) {
            this.voiceSelect.value = voice.name;
        }

        // 通知后台
        this.dependencies.chrome.runtime.sendMessage({
            action: 'setVoice',
            voice: voice
        });

        console.log('已选择语音:', voice.name);
    }

    // 加载默认语音
    async loadDefaultVoice() {
        try {
            const result = await this.dependencies.chrome.storage.local.get(['defaultVoice']);
            if (result.defaultVoice) {
                const voice = this.availableVoices.find(v => v.name === result.defaultVoice);
                if (voice) {
                    this.selectVoice(voice);
                }
            }
        } catch (error) {
            this.errorHandler.handleError(error, 'load_default_voice');
        }
    }

    // 初始化语音列表
    initializeVoiceList() {
        // 获取可用语音
        this.dependencies.chrome.tts.getVoices((voices) => {
            this.availableVoices = voices || [];
            this.renderFavoriteVoices();
            this.renderAvailableVoices();
            this.populateVoiceSelect();
        });
    }

    // 填充语音选择下拉框
    populateVoiceSelect() {
        if (!this.voiceSelect) return;

        this.voiceSelect.innerHTML = '<option value="">选择语音</option>';
        
        this.availableVoices.forEach(voice => {
            const option = document.createElement('option');
            option.value = voice.name;
            option.textContent = `${voice.name} (${this.getLangName(voice.lang)})`;
            this.voiceSelect.appendChild(option);
        });
    }

    // 公共API
    getAvailableVoices() {
        return this.availableVoices;
    }

    getFavoriteVoices() {
        return this.favoriteVoices;
    }

    getCurrentVoice() {
        return this.currentVoice;
    }

    setCurrentVoice(voice) {
        this.currentVoice = voice;
        this.renderFavoriteVoices();
        this.renderAvailableVoices();
    }
}
