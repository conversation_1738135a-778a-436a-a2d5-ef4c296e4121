// 神灯AI·灵阅 - 简化版内容解析脚本
console.log("🔧 神灯AI·灵阅：内容解析脚本已加载");

// 检查API可用性
const isRuntimeAvailable = typeof chrome !== 'undefined' && typeof chrome.runtime !== 'undefined';

if (!isRuntimeAvailable) {
  console.warn("⚠️ Chrome Runtime API不可用");
}

// 解析页面内容的函数
function parsePageContent() {
  console.log("📖 开始解析页面内容");
  console.log("🌐 当前页面URL:", window.location.href);
  console.log("📄 页面标题:", document.title);

  try {
    // 检查 Readability 库是否可用
    if (typeof Readability === 'undefined') {
      console.error("❌ Readability库未加载");
      return;
    }

    console.log("✅ Readability库已加载");

    // 克隆 document 对象
    const documentClone = document.cloneNode(true);
    console.log("📋 文档已克隆");

    // 创建 Readability 实例
    const reader = new Readability(documentClone, {
      classesToPreserve: ['article', 'content', 'main', 'text', 'story']
    });

    console.log("🔧 Readability实例已创建");

    // 解析文章
    const article = reader.parse();
    console.log("🔍 Readability解析完成");

    if (article && article.textContent) {
      console.log("✅ 成功解析文章:");
      console.log("  📖 标题:", article.title);
      console.log("  📝 文本长度:", article.textContent.length);
      console.log("  👤 作者:", article.byline);

      // 发送解析结果给背景脚本
      if (isRuntimeAvailable) {
        console.log("📤 发送解析结果到背景脚本");
        chrome.runtime.sendMessage({
          action: "parsedContent",
          article: article
        }).then(() => {
          console.log("✅ 解析结果发送成功");
        }).catch(error => {
          console.error("❌ 发送解析结果失败:", error);
        });
      } else {
        console.error("❌ Chrome Runtime API不可用");
      }
    } else {
      console.warn("⚠️ Readability 未能提取文章内容");
      console.log("🔍 尝试基础内容提取...");

      // 尝试基础的内容提取
      const bodyText = document.body.innerText;
      if (bodyText && bodyText.length > 100) {
        console.log("📝 使用基础文本提取，长度:", bodyText.length);
        const basicArticle = {
          title: document.title,
          textContent: bodyText,
          content: document.body.innerHTML,
          byline: "",
          excerpt: bodyText.substring(0, 200) + "..."
        };

        if (isRuntimeAvailable) {
          chrome.runtime.sendMessage({
            action: "parsedContent",
            article: basicArticle
          }).catch(error => {
            console.error("❌ 发送基础解析结果失败:", error);
          });
        }
      }
    }
  } catch (error) {
    console.error("❌ 解析页面时出错:", error);
    console.error("错误详情:", error.stack);
  }
}

// 消息监听器
if (isRuntimeAvailable) {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("📨 收到消息:", message.action);

    if (message.action === 'parseContent') {
      console.log("🔄 开始解析页面内容");
      parsePageContent();
      sendResponse({ status: "解析已开始" });
    }
  });
}

// 页面加载完成后自动解析一次（为了缓存内容）
if (document.readyState === 'complete') {
  console.log("📄 页面已完全加载，自动解析内容");
  setTimeout(() => {
    parsePageContent();
  }, 1000); // 延迟1秒确保页面稳定
} else {
  document.addEventListener('load', () => {
    console.log("📄 页面加载完成，自动解析内容");
    setTimeout(() => {
      parsePageContent();
    }, 1000);
  });
}

console.log("✅ 神灯AI·灵阅：内容解析脚本初始化完成");
