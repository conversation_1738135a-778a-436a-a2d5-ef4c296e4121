<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块系统测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .mock-ui {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            background: #fafafa;
        }
        .mock-button {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #28a745;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            user-select: none;
        }
        .mock-button:hover {
            background: #218838;
        }
        .mock-button.playing {
            background: #dc3545;
        }
        .mock-slider {
            width: 200px;
            margin: 10px;
        }
        .mock-status {
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px;
        }
        .mock-status.idle {
            background: #6c757d;
            color: white;
        }
        .mock-status.reading {
            background: #28a745;
            color: white;
        }
        .mock-status.paused {
            background: #ffc107;
            color: black;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 神灯AI·灵阅 模块系统测试</h1>
        <p>这个页面用于测试新的模块化架构，验证各个组件的功能。</p>

        <!-- 模块系统状态 -->
        <div class="test-section">
            <h3>📦 模块系统状态</h3>
            <button onclick="checkModuleSystem()">检查模块系统</button>
            <button onclick="toggleModuleSystem()">切换模块系统</button>
            <div id="module-status" class="status info">等待检查...</div>
        </div>

        <!-- 基础工具类测试 -->
        <div class="test-section">
            <h3>🔧 基础工具类测试</h3>
            <button onclick="testResourceManager()">测试资源管理器</button>
            <button onclick="testStateManager()">测试状态管理器</button>
            <button onclick="testConfigManager()">测试配置管理器</button>
            <button onclick="testPerformanceMonitor()">测试性能监控</button>
            <button onclick="testErrorHandler()">测试错误处理</button>
            <div id="utils-status" class="status info">点击按钮开始测试...</div>
        </div>

        <!-- UI控制器测试 -->
        <div class="test-section">
            <h3>🎮 UI控制器测试</h3>
            <div class="mock-ui">
                <h4>模拟UI界面</h4>
                <div class="mock-button" id="mock-play-btn">▶ 播放</div>
                <div class="mock-button" id="mock-stop-btn">⏹ 停止</div>
                <div class="mock-status idle" id="mock-status">空闲</div>
                <br>
                <label>语速: </label>
                <input type="range" class="mock-slider" id="mock-speed" min="0.5" max="3.0" step="0.1" value="1.0">
                <span id="mock-speed-value">1.0x</span>
            </div>
            <button onclick="testUIController()">测试UI控制器</button>
            <button onclick="simulateUserInteraction()">模拟用户交互</button>
            <div id="ui-status" class="status info">准备测试UI控制器...</div>
        </div>

        <!-- 性能测试 -->
        <div class="test-section">
            <h3>⚡ 性能测试</h3>
            <button onclick="runPerformanceTest()">运行性能测试</button>
            <button onclick="runMemoryTest()">内存泄漏测试</button>
            <div id="performance-status" class="status info">准备运行性能测试...</div>
        </div>

        <!-- 日志输出 -->
        <div class="test-section">
            <h3>📋 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="test-log" class="log">等待测试开始...</div>
        </div>
    </div>

    <script type="module">
        // 测试日志功能
        function log(message, type = 'info') {
            const logEl = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logEl.textContent += logEntry;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[TEST] ${message}`);
        }

        function updateStatus(elementId, message, type = 'info') {
            const el = document.getElementById(elementId);
            el.textContent = message;
            el.className = `status ${type}`;
        }

        // 检查模块系统
        window.checkModuleSystem = async function() {
            log('开始检查模块系统...');
            
            try {
                // 检查是否在Chrome扩展环境中
                if (typeof chrome === 'undefined') {
                    throw new Error('不在Chrome扩展环境中，无法测试完整功能');
                }

                // 动态导入模块
                const { configManager } = await import('./src/utils/configManager.js');
                const { moduleContainer } = await import('./src/ui/sidepanel/modules/ModuleContainer.js');
                
                log('✅ 成功导入基础模块');
                
                // 检查配置
                const useNewModules = configManager.getConfig('debug.useNewModules', false);
                log(`当前模块系统状态: ${useNewModules ? '启用' : '禁用'}`);
                
                // 检查模块容器状态
                const containerStatus = moduleContainer.getStatus();
                log(`模块容器状态: ${JSON.stringify(containerStatus)}`);
                
                updateStatus('module-status', `模块系统${useNewModules ? '已启用' : '已禁用'}，容器已加载`, 'success');
                
            } catch (error) {
                log(`❌ 模块系统检查失败: ${error.message}`, 'error');
                updateStatus('module-status', `检查失败: ${error.message}`, 'error');
            }
        };

        // 切换模块系统
        window.toggleModuleSystem = async function() {
            log('切换模块系统状态...');
            
            try {
                const { configManager } = await import('./src/utils/configManager.js');
                const { moduleContainer } = await import('./src/ui/sidepanel/modules/ModuleContainer.js');
                
                const currentState = configManager.getConfig('debug.useNewModules', false);
                const newState = !currentState;
                
                if (newState) {
                    moduleContainer.enableNewModules();
                    log('✅ 新模块系统已启用');
                } else {
                    moduleContainer.disableNewModules();
                    log('❌ 新模块系统已禁用');
                }
                
                updateStatus('module-status', `模块系统已${newState ? '启用' : '禁用'}`, newState ? 'success' : 'info');
                
            } catch (error) {
                log(`❌ 切换失败: ${error.message}`, 'error');
                updateStatus('module-status', `切换失败: ${error.message}`, 'error');
            }
        };

        // 测试资源管理器
        window.testResourceManager = async function() {
            log('测试资源管理器...');
            
            try {
                const { resourceManager } = await import('./src/utils/resourceManager.js');
                
                // 测试定时器管理
                const timerId = resourceManager.createTimeout('test-timer', () => {
                    log('✅ 定时器执行成功');
                }, 1000);
                
                log(`创建定时器: ${timerId}`);
                
                // 测试间隔器管理
                let count = 0;
                const intervalId = resourceManager.createInterval('test-interval', () => {
                    count++;
                    log(`间隔器执行: ${count}`);
                    if (count >= 3) {
                        resourceManager.clearInterval('test-interval');
                        log('✅ 间隔器测试完成');
                    }
                }, 500);
                
                log(`创建间隔器: ${intervalId}`);
                
                // 测试资源清理
                setTimeout(() => {
                    const status = resourceManager.getResourceStatus();
                    log(`资源状态: ${JSON.stringify(status)}`);
                    updateStatus('utils-status', '资源管理器测试完成', 'success');
                }, 3000);
                
            } catch (error) {
                log(`❌ 资源管理器测试失败: ${error.message}`, 'error');
                updateStatus('utils-status', `测试失败: ${error.message}`, 'error');
            }
        };

        // 测试状态管理器
        window.testStateManager = async function() {
            log('测试状态管理器...');
            
            try {
                const { stateManager } = await import('./src/utils/stateManager.js');
                
                // 订阅状态变化
                const unsubscribe = stateManager.subscribe((state, changes) => {
                    log(`状态变化: ${changes.join(', ')}`);
                    log(`新状态: ${JSON.stringify(state)}`);
                });
                
                // 测试状态更新
                stateManager.updateState({
                    testValue: 'hello',
                    readingState: 'testing'
                });
                
                // 测试批量更新
                stateManager.batchUpdate(() => {
                    stateManager.updateState({ testValue: 'world' });
                    stateManager.updateState({ readingState: 'batch-test' });
                });
                
                setTimeout(() => {
                    unsubscribe();
                    log('✅ 状态管理器测试完成');
                    updateStatus('utils-status', '状态管理器测试完成', 'success');
                }, 1000);
                
            } catch (error) {
                log(`❌ 状态管理器测试失败: ${error.message}`, 'error');
                updateStatus('utils-status', `测试失败: ${error.message}`, 'error');
            }
        };

        // 测试配置管理器
        window.testConfigManager = async function() {
            log('测试配置管理器...');
            
            try {
                const { configManager } = await import('./src/utils/configManager.js');
                
                // 测试配置设置和获取
                configManager.setConfig('test.value', 'test-data');
                const value = configManager.getConfig('test.value');
                log(`配置测试: 设置值 'test-data', 获取值 '${value}'`);
                
                // 测试默认值
                const defaultValue = configManager.getConfig('test.nonexistent', 'default');
                log(`默认值测试: ${defaultValue}`);
                
                // 测试配置监听
                const unwatch = configManager.watchConfig('test.watched', (newValue, oldValue) => {
                    log(`配置变化: ${oldValue} -> ${newValue}`);
                });
                
                configManager.setConfig('test.watched', 'new-value');
                
                setTimeout(() => {
                    unwatch();
                    log('✅ 配置管理器测试完成');
                    updateStatus('utils-status', '配置管理器测试完成', 'success');
                }, 500);
                
            } catch (error) {
                log(`❌ 配置管理器测试失败: ${error.message}`, 'error');
                updateStatus('utils-status', `测试失败: ${error.message}`, 'error');
            }
        };

        // 测试性能监控
        window.testPerformanceMonitor = async function() {
            log('测试性能监控器...');
            
            try {
                const { performanceMonitor } = await import('./src/utils/performanceMonitor.js');
                
                // 测试计时器
                performanceMonitor.startTimer('test-operation');
                
                // 模拟一些操作
                await new Promise(resolve => setTimeout(resolve, 100));
                
                const timing = performanceMonitor.endTimer('test-operation');
                log(`操作耗时: ${timing.duration.toFixed(2)}ms`);
                
                // 测试内存监控
                const memoryInfo = performanceMonitor.getMemoryInfo();
                log(`内存信息: ${JSON.stringify(memoryInfo)}`);
                
                // 测试性能报告
                const report = performanceMonitor.getPerformanceReport();
                log(`性能报告: ${JSON.stringify(report)}`);
                
                log('✅ 性能监控器测试完成');
                updateStatus('utils-status', '性能监控器测试完成', 'success');
                
            } catch (error) {
                log(`❌ 性能监控器测试失败: ${error.message}`, 'error');
                updateStatus('utils-status', `测试失败: ${error.message}`, 'error');
            }
        };

        // 测试错误处理器
        window.testErrorHandler = async function() {
            log('测试错误处理器...');
            
            try {
                const { errorHandler } = await import('./src/utils/errorHandler.js');
                
                // 测试错误处理
                const testError = new Error('这是一个测试错误');
                errorHandler.handleError(testError, 'test-context', { testData: 'value' });
                
                // 测试错误统计
                const stats = errorHandler.getErrorStats();
                log(`错误统计: ${JSON.stringify(stats)}`);
                
                // 测试错误历史
                const history = errorHandler.getErrorHistory();
                log(`错误历史: ${history.length} 条记录`);
                
                log('✅ 错误处理器测试完成');
                updateStatus('utils-status', '错误处理器测试完成', 'success');
                
            } catch (error) {
                log(`❌ 错误处理器测试失败: ${error.message}`, 'error');
                updateStatus('utils-status', `测试失败: ${error.message}`, 'error');
            }
        };

        // 测试UI控制器
        window.testUIController = async function() {
            log('测试UI控制器...');
            
            try {
                const { moduleContainer } = await import('./src/ui/sidepanel/modules/ModuleContainer.js');
                const { UIController } = await import('./src/ui/sidepanel/modules/UIController.js');
                
                // 注册UI控制器
                const uiController = moduleContainer.registerModule('UIController', UIController);
                
                // 初始化模块
                await moduleContainer.initializeAll();
                
                log('✅ UI控制器创建和初始化成功');
                
                // 获取模块状态
                const status = uiController.getStatus();
                log(`UI控制器状态: ${JSON.stringify(status)}`);
                
                updateStatus('ui-status', 'UI控制器测试完成', 'success');
                
            } catch (error) {
                log(`❌ UI控制器测试失败: ${error.message}`, 'error');
                updateStatus('ui-status', `测试失败: ${error.message}`, 'error');
            }
        };

        // 模拟用户交互
        window.simulateUserInteraction = function() {
            log('模拟用户交互...');
            
            const playBtn = document.getElementById('mock-play-btn');
            const stopBtn = document.getElementById('mock-stop-btn');
            const status = document.getElementById('mock-status');
            const speedSlider = document.getElementById('mock-speed');
            const speedValue = document.getElementById('mock-speed-value');
            
            let isPlaying = false;
            
            // 播放/暂停按钮
            playBtn.onclick = function() {
                isPlaying = !isPlaying;
                if (isPlaying) {
                    this.textContent = '⏸ 暂停';
                    this.classList.add('playing');
                    status.textContent = '朗读中';
                    status.className = 'mock-status reading';
                    log('🎵 开始播放');
                } else {
                    this.textContent = '▶ 播放';
                    this.classList.remove('playing');
                    status.textContent = '已暂停';
                    status.className = 'mock-status paused';
                    log('⏸ 暂停播放');
                }
            };
            
            // 停止按钮
            stopBtn.onclick = function() {
                isPlaying = false;
                playBtn.textContent = '▶ 播放';
                playBtn.classList.remove('playing');
                status.textContent = '空闲';
                status.className = 'mock-status idle';
                log('⏹ 停止播放');
            };
            
            // 语速滑块
            speedSlider.oninput = function() {
                const speed = parseFloat(this.value);
                speedValue.textContent = `${speed.toFixed(1)}x`;
                log(`🎚 语速调整: ${speed.toFixed(1)}x`);
            };
            
            log('✅ 用户交互模拟已设置');
            updateStatus('ui-status', '用户交互模拟已激活', 'success');
        };

        // 性能测试
        window.runPerformanceTest = async function() {
            log('运行性能测试...');
            updateStatus('performance-status', '正在运行性能测试...', 'info');
            
            try {
                const { performanceMonitor } = await import('./src/utils/performanceMonitor.js');
                
                // 测试模块加载性能
                performanceMonitor.startTimer('module-load-test');
                
                for (let i = 0; i < 10; i++) {
                    const { moduleContainer } = await import('./src/ui/sidepanel/modules/ModuleContainer.js');
                    const { UIController } = await import('./src/ui/sidepanel/modules/UIController.js');
                }
                
                const loadTiming = performanceMonitor.endTimer('module-load-test');
                log(`模块加载测试: ${loadTiming.duration.toFixed(2)}ms`);
                
                // 测试大量操作性能
                performanceMonitor.startTimer('bulk-operations');
                
                const operations = [];
                for (let i = 0; i < 1000; i++) {
                    operations.push(new Promise(resolve => {
                        setTimeout(resolve, Math.random() * 10);
                    }));
                }
                
                await Promise.all(operations);
                
                const bulkTiming = performanceMonitor.endTimer('bulk-operations');
                log(`批量操作测试: ${bulkTiming.duration.toFixed(2)}ms`);
                
                const report = performanceMonitor.getPerformanceReport();
                log(`性能报告: ${JSON.stringify(report)}`);
                
                updateStatus('performance-status', '性能测试完成', 'success');
                
            } catch (error) {
                log(`❌ 性能测试失败: ${error.message}`, 'error');
                updateStatus('performance-status', `测试失败: ${error.message}`, 'error');
            }
        };

        // 内存泄漏测试
        window.runMemoryTest = async function() {
            log('运行内存泄漏测试...');
            updateStatus('performance-status', '正在检查内存泄漏...', 'info');
            
            try {
                const { resourceManager } = await import('./src/utils/resourceManager.js');
                const { moduleContainer } = await import('./src/ui/sidepanel/modules/ModuleContainer.js');
                
                const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
                log(`初始内存使用: ${(initialMemory / 1024 / 1024).toFixed(2)}MB`);
                
                // 创建和销毁大量资源
                for (let i = 0; i < 100; i++) {
                    // 创建定时器
                    resourceManager.createTimeout(`test-${i}`, () => {}, 1000);
                    
                    // 创建间隔器
                    resourceManager.createInterval(`interval-${i}`, () => {}, 100);
                    
                    // 立即清理
                    resourceManager.clearTimeout(`test-${i}`);
                    resourceManager.clearInterval(`interval-${i}`);
                }
                
                // 强制垃圾回收（如果可用）
                if (window.gc) {
                    window.gc();
                }
                
                setTimeout(() => {
                    const finalMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
                    const memoryDiff = finalMemory - initialMemory;
                    
                    log(`最终内存使用: ${(finalMemory / 1024 / 1024).toFixed(2)}MB`);
                    log(`内存差异: ${(memoryDiff / 1024 / 1024).toFixed(2)}MB`);
                    
                    if (memoryDiff < 1024 * 1024) { // 小于1MB认为正常
                        log('✅ 内存泄漏测试通过');
                        updateStatus('performance-status', '内存泄漏测试通过', 'success');
                    } else {
                        log('⚠️ 可能存在内存泄漏');
                        updateStatus('performance-status', '可能存在内存泄漏', 'error');
                    }
                }, 2000);
                
            } catch (error) {
                log(`❌ 内存测试失败: ${error.message}`, 'error');
                updateStatus('performance-status', `测试失败: ${error.message}`, 'error');
            }
        };

        // 清空日志
        window.clearLog = function() {
            document.getElementById('test-log').textContent = '';
            log('日志已清空');
        };

        // 页面加载完成后自动检查模块系统
        window.addEventListener('load', () => {
            log('测试页面加载完成');
            setTimeout(checkModuleSystem, 1000);
        });
    </script>
</body>
</html>
