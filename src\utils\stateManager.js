// 统一状态管理器
class StateManager {
    constructor() {
        this.state = {};
        this.listeners = new Map();
        this.updateQueue = [];
        this.isUpdating = false;
    }

    // 获取状态
    getState(key = null) {
        if (key) {
            return this.state[key];
        }
        return { ...this.state }; // 返回状态副本
    }

    // 设置状态（支持批量更新）
    setState(updates, callback = null) {
        if (typeof updates === 'function') {
            updates = updates(this.state);
        }

        // 添加到更新队列
        this.updateQueue.push({ updates, callback });
        
        // 如果没有正在更新，开始处理队列
        if (!this.isUpdating) {
            this.processUpdateQueue();
        }
    }

    // 处理更新队列（防止竞态条件）
    async processUpdateQueue() {
        this.isUpdating = true;

        while (this.updateQueue.length > 0) {
            const { updates, callback } = this.updateQueue.shift();
            
            // 计算状态变化
            const prevState = { ...this.state };
            const newState = { ...this.state, ...updates };
            
            // 检查是否有实际变化
            const hasChanges = Object.keys(updates).some(
                key => prevState[key] !== newState[key]
            );

            if (hasChanges) {
                this.state = newState;
                
                // 通知监听器
                this.notifyListeners(updates, prevState);
                
                // 执行回调
                if (callback) {
                    try {
                        await callback(newState, prevState);
                    } catch (error) {
                        console.error('状态更新回调执行失败:', error);
                    }
                }
            }
        }

        this.isUpdating = false;
    }

    // 订阅状态变化
    subscribe(listener, keys = null) {
        const id = Date.now() + Math.random();
        this.listeners.set(id, { listener, keys });
        
        // 返回取消订阅函数
        return () => {
            this.listeners.delete(id);
        };
    }

    // 通知监听器
    notifyListeners(updates, prevState) {
        this.listeners.forEach(({ listener, keys }) => {
            try {
                // 如果指定了监听的键，只在相关键变化时通知
                if (keys) {
                    const relevantChanges = keys.some(key => key in updates);
                    if (!relevantChanges) return;
                }
                
                listener(this.state, updates, prevState);
            } catch (error) {
                console.error('状态监听器执行失败:', error);
            }
        });
    }

    // 重置状态
    reset(initialState = {}) {
        const prevState = { ...this.state };
        this.state = { ...initialState };
        this.notifyListeners(this.state, prevState);
    }

    // 获取状态统计信息
    getStats() {
        return {
            stateKeys: Object.keys(this.state).length,
            listeners: this.listeners.size,
            queueLength: this.updateQueue.length,
            isUpdating: this.isUpdating
        };
    }
}

// 创建全局状态管理器实例
export const stateManager = new StateManager();

// 初始化默认状态
stateManager.setState({
    readingState: 'idle',
    currentSpeed: 1.1,
    currentVoice: null,
    continuousReading: true,
    article: null,
    error: null,
    continuousChapters: 30,
    readingDuration: 30 * 60, // 30分钟
    isBackgroundPlaying: false,
    currentChapterTitle: null,
    nextChapterInfo: null,
    remainingPlaybackTime: 0
});

console.log("状态管理器已初始化");
