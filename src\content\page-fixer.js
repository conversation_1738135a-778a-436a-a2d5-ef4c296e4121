// 页面修复脚本 - 解决常见的网页JavaScript错误
console.log("神灯AI·灵阅：页面修复脚本已加载");

// 修复jQuery未定义错误
if (typeof $ === 'undefined' && typeof jQuery === 'undefined') {
  console.log("神灯AI·灵阅：检测到页面缺少jQuery，创建简单的替代");
  
  // 创建一个最简单的jQuery替代，只包含基本功能
  window.$ = window.jQuery = function(selector) {
    if (typeof selector === 'string') {
      return {
        length: 0,
        each: function() { return this; },
        on: function() { return this; },
        off: function() { return this; },
        click: function() { return this; },
        hide: function() { return this; },
        show: function() { return this; },
        addClass: function() { return this; },
        removeClass: function() { return this; },
        attr: function() { return this; },
        val: function() { return ''; },
        text: function() { return ''; },
        html: function() { return ''; }
      };
    } else if (typeof selector === 'function') {
      // 文档就绪函数
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', selector);
      } else {
        selector();
      }
    }
    return this;
  };
  
  // 添加一些常用的jQuery静态方法
  $.extend = function(target, source) {
    for (let key in source) {
      if (source.hasOwnProperty(key)) {
        target[key] = source[key];
      }
    }
    return target;
  };
  
  $.ajax = function(options) {
    console.warn("神灯AI·灵阅：页面尝试使用jQuery.ajax，但功能不完整");
    return {
      done: function() { return this; },
      fail: function() { return this; },
      always: function() { return this; }
    };
  };
  
  console.log("神灯AI·灵阅：jQuery替代已创建，可能解决部分页面错误");
}

// 捕获并静默处理常见的页面错误
window.addEventListener('error', function(event) {
  const error = event.error;
  const message = event.message;
  
  // 静默处理jQuery相关错误
  if (message && (
    message.includes('$ is not defined') ||
    message.includes('jQuery is not defined') ||
    message.includes('pc-novel.js')
  )) {
    console.log("神灯AI·灵阅：已静默处理页面jQuery错误:", message);
    event.preventDefault();
    return false;
  }
  
  // 静默处理资源加载错误
  if (message && (
    message.includes('Failed to load resource') ||
    message.includes('404') ||
    message.includes('all.gif')
  )) {
    console.log("神灯AI·灵阅：已静默处理页面资源加载错误:", message);
    event.preventDefault();
    return false;
  }
});

// 捕获Promise rejection错误
window.addEventListener('unhandledrejection', function(event) {
  const reason = event.reason;
  
  if (reason && typeof reason === 'object' && reason.message) {
    // 静默处理消息通道相关错误
    if (reason.message.includes('message channel closed') ||
        reason.message.includes('Extension context invalidated')) {
      console.log("神灯AI·灵阅：已静默处理扩展消息错误:", reason.message);
      event.preventDefault();
      return false;
    }
  }
});

console.log("神灯AI·灵阅：页面修复脚本初始化完成");
