{"env": {"browser": true, "es2021": true, "webextensions": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2021, "sourceType": "module"}, "rules": {"no-console": ["warn", {"allow": ["error", "warn"]}], "no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "no-undef": "error", "no-eval": "error", "no-implied-eval": "error", "prefer-const": "warn", "no-var": "warn", "eqeqeq": "warn", "curly": "warn", "no-trailing-spaces": "warn", "indent": ["warn", 2], "quotes": ["warn", "single", {"avoidEscape": true}], "semi": ["warn", "always"]}, "globals": {"chrome": "readonly", "Readability": "readonly"}, "ignorePatterns": ["src/lib/**", "node_modules/**", "*.min.js"]}